# AutoGen编程工作流简化部署指南

## 🎯 部署目标

将AutoGen编程工作流部署为两个独立的服务：
- **API服务**: `api.crawlonline.vip:8000`
- **Web服务**: `chat.crawlonline.vip:8080`

## 📋 前提条件

- Python 3.10+
- 您现有的Nginx配置
- 服务器访问权限

## 🚀 快速部署

### 1. 准备环境

```bash
# 安装Python依赖
pip install --user fastapi uvicorn jinja2 python-multipart openai itsdangerous

# 验证安装
python -c "import fastapi, uvicorn, jinja2, openai; print('依赖安装成功')"
```

### 2. 启动服务

#### 方法A: 使用启动脚本
```bash
python start_services.py
```

#### 方法B: 分别启动
```bash
# 启动API服务 (后台运行)
nohup python simple_api_server.py > api.log 2>&1 &

# 启动Web服务 (后台运行)
nohup python simple_web_server.py > web.log 2>&1 &
```

#### 方法C: 使用systemd服务
```bash
# 创建API服务
sudo tee /etc/systemd/system/autogen-api.service > /dev/null <<EOF
[Unit]
Description=AutoGen API Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(which python) simple_api_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 创建Web服务
sudo tee /etc/systemd/system/autogen-web.service > /dev/null <<EOF
[Unit]
Description=AutoGen Web Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=$(which python) simple_web_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable autogen-api autogen-web
sudo systemctl start autogen-api autogen-web
```

### 3. 验证服务

```bash
# 检查API服务
curl http://localhost:8000/health

# 检查Web服务
curl http://localhost:8080/

# 检查进程
ps aux | grep "simple_.*_server"
```

## 🌐 Nginx配置

### 添加到您现有的Nginx配置

```nginx
# 上游服务器定义
upstream autogen_api {
    server 127.0.0.1:8000;
    keepalive 32;
}

upstream autogen_web {
    server 127.0.0.1:8080;
    keepalive 32;
}

# API服务配置
server {
    listen 80;
    server_name api.crawlonline.vip;
    
    # 如果您有SSL，添加以下行并配置证书
    # listen 443 ssl http2;
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass http://autogen_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
    
    # API文档路径
    location /docs {
        proxy_pass http://autogen_api/docs;
    }
    
    location /redoc {
        proxy_pass http://autogen_api/redoc;
    }
}

# Web服务配置
server {
    listen 80;
    server_name chat.crawlonline.vip;
    
    # 如果您有SSL，添加以下行并配置证书
    # listen 443 ssl http2;
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass http://autogen_web;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
}
```

### 重新加载Nginx

```bash
# 测试配置
sudo nginx -t

# 重新加载配置
sudo nginx -s reload
```

## 🔧 配置说明

### 服务配置 (config.py)

主要配置项已在 `config.py` 中设置：

```python
@dataclass
class WebServiceConfig:
    # API服务配置
    api_domain: str = "api.crawlonline.vip"
    api_port: int = 8000
    api_key: str = "sk-autogen-api-2024-secure-key"
    
    # Web服务配置
    web_domain: str = "chat.crawlonline.vip"
    web_port: int = 8080
    
    # 登录配置
    username: str = "admin"
    password: str = "autogen2024"
```

### 环境变量

```bash
# 设置DeepSeek API密钥
export DEEPSEEK_API_KEY="***********************************"
```

## 🔍 服务监控

### 检查服务状态

```bash
# 使用systemd
sudo systemctl status autogen-api
sudo systemctl status autogen-web

# 检查端口
netstat -tlnp | grep :8000
netstat -tlnp | grep :8080

# 查看日志
journalctl -u autogen-api -f
journalctl -u autogen-web -f
```

### 性能监控

```bash
# 检查资源使用
top -p $(pgrep -f simple_api_server)
top -p $(pgrep -f simple_web_server)

# 检查连接数
ss -tuln | grep :8000
ss -tuln | grep :8080
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :8000
   sudo lsof -i :8080
   ```

2. **服务无法启动**
   ```bash
   # 检查Python路径
   which python
   
   # 检查依赖
   python -c "import fastapi"
   ```

3. **Nginx代理失败**
   ```bash
   # 检查上游服务
   curl http://localhost:8000/health
   curl http://localhost:8080/
   
   # 检查Nginx错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

### 重启服务

```bash
# 使用systemd
sudo systemctl restart autogen-api autogen-web

# 手动重启
pkill -f simple_api_server
pkill -f simple_web_server
nohup python simple_api_server.py > api.log 2>&1 &
nohup python simple_web_server.py > web.log 2>&1 &
```

## 🔒 安全建议

1. **防火墙配置**
   ```bash
   # 只允许Nginx访问应用端口
   sudo ufw deny 8000
   sudo ufw deny 8080
   sudo ufw allow from 127.0.0.1 to any port 8000
   sudo ufw allow from 127.0.0.1 to any port 8080
   ```

2. **API密钥管理**
   - 定期更换API密钥
   - 使用环境变量存储敏感信息
   - 监控API使用情况

3. **日志管理**
   ```bash
   # 设置日志轮转
   sudo tee /etc/logrotate.d/autogen > /dev/null <<EOF
   /path/to/your/project/*.log {
       daily
       missingok
       rotate 7
       compress
       delaycompress
       notifempty
       copytruncate
   }
   EOF
   ```

## ✅ 部署验证

### 功能测试

```bash
# 1. 测试API健康检查
curl http://api.crawlonline.vip/health

# 2. 测试API认证
curl -H "Authorization: Bearer sk-autogen-api-2024-secure-key" \
     http://api.crawlonline.vip/

# 3. 测试Web界面
curl http://chat.crawlonline.vip/

# 4. 测试编程任务API
curl -X POST "http://api.crawlonline.vip/api/v1/programming-task" \
  -H "Authorization: Bearer sk-autogen-api-2024-secure-key" \
  -H "Content-Type: application/json" \
  -d '{"task": "实现一个简单的Hello World函数", "config_name": "fast"}'
```

### 访问测试

1. **Web界面**: 访问 `http://chat.crawlonline.vip`
   - 用户名: `admin`
   - 密码: `autogen2024`

2. **API文档**: 访问 `http://api.crawlonline.vip/docs`

## 🎉 部署完成

恭喜！您的AutoGen编程工作流已成功部署。服务现在可以通过以下方式访问：

- **API服务**: `http://api.crawlonline.vip`
- **Web界面**: `http://chat.crawlonline.vip`
- **API文档**: `http://api.crawlonline.vip/docs`

您的现有Nginx配置保持不变，只需添加上述代理配置即可。
