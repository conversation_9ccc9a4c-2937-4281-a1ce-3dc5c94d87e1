# Docker部署问题修复指南

## 🐛 问题分析

根据您提供的错误日志：

```
autogen-web  | ModuleNotFoundError: No module named 'itsdangerous'
```

问题原因：
1. Docker容器中缺少 `itsdangerous` 依赖包
2. `requirements.txt` 中没有包含这个必需的依赖
3. Starlette的SessionMiddleware需要这个包来处理会话加密

## ✅ 修复方案

### 1. 已修复的文件

#### `requirements-docker.txt` (新增)
```txt
# Docker部署专用依赖包 - 精简版本

# OpenAI API客户端 (兼容DeepSeek API)
openai>=1.0.0

# Web服务框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
jinja2>=3.1.0
python-multipart>=0.0.6

# 会话和安全
itsdangerous>=2.1.0

# 基础依赖
pydantic>=2.0.0
```

#### `Dockerfile` (已更新)
```dockerfile
# 复制依赖文件
COPY requirements-docker.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements-docker.txt
```

#### `docker-compose.yml` (已修复)
```yaml
# 移除了过时的 version 字段

services:
  autogen-api:
    build: .
    container_name: autogen-api
    ports:
      - "8000:8000"
    environment:
      - DEEPSEEK_API_KEY=***********************************
    command: ["python", "simple_api_server.py"]
    restart: unless-stopped

  autogen-web:
    build: .
    container_name: autogen-web
    ports:
      - "8080:8080"
    environment:
      - DEEPSEEK_API_KEY=***********************************
    command: ["python", "simple_web_server.py"]
    restart: unless-stopped
    depends_on:
      - autogen-api
```

## 🚀 重新部署步骤

### 1. 停止现有容器
```bash
docker compose down
```

### 2. 清理旧镜像（可选）
```bash
# 删除旧镜像
docker rmi augment-work-autogen-api augment-work-autogen-web

# 或者清理所有未使用的镜像
docker image prune -f
```

### 3. 重新构建和启动
```bash
# 重新构建镜像
docker compose build --no-cache

# 启动服务
docker compose up -d
```

### 4. 验证部署
```bash
# 查看容器状态
docker compose ps

# 查看日志
docker compose logs autogen-api
docker compose logs autogen-web

# 测试服务
curl http://localhost:8000/health
curl http://localhost:8080/
```

## 🔍 故障排除

### 检查容器状态
```bash
# 查看所有容器
docker compose ps

# 查看详细日志
docker compose logs -f autogen-web
docker compose logs -f autogen-api
```

### 进入容器调试
```bash
# 进入Web服务容器
docker compose exec autogen-web bash

# 检查Python包
docker compose exec autogen-web pip list | grep itsdangerous

# 手动测试导入
docker compose exec autogen-web python -c "import itsdangerous; print('OK')"
```

### 网络连接测试
```bash
# 测试API服务
curl http://localhost:8000/health

# 测试Web服务
curl http://localhost:8080/

# 测试API功能
curl -X POST "http://localhost:8000/api/v1/programming-task" \
  -H "Authorization: Bearer sk-autogen-api-2024-secure-key" \
  -H "Content-Type: application/json" \
  -d '{"task": "实现一个Hello World函数", "config_name": "fast"}'
```

## 📋 完整的部署验证清单

### ✅ 容器启动检查
- [ ] `autogen-api` 容器状态为 `Up`
- [ ] `autogen-web` 容器状态为 `Up`
- [ ] 端口映射正确 (8000, 8080)

### ✅ 服务功能检查
- [ ] API健康检查: `curl http://localhost:8000/health`
- [ ] Web界面访问: `curl http://localhost:8080/`
- [ ] API文档访问: `http://localhost:8000/docs`

### ✅ 日志检查
- [ ] 无错误日志输出
- [ ] 服务启动消息正常
- [ ] 无依赖包缺失错误

## 🔧 优化建议

### 1. 生产环境优化

```dockerfile
# 使用多阶段构建减小镜像大小
FROM python:3.11-slim as builder

WORKDIR /app
COPY requirements-docker.txt .
RUN pip install --user --no-cache-dir -r requirements-docker.txt

FROM python:3.11-slim
WORKDIR /app

# 复制已安装的包
COPY --from=builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 autogen && \
    chown -R autogen:autogen /app
USER autogen

EXPOSE 8000 8080
CMD ["python", "simple_api_server.py"]
```

### 2. 环境变量配置

```yaml
# docker-compose.yml
services:
  autogen-api:
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEBUG=false
      - LOG_LEVEL=info
```

### 3. 健康检查配置

```yaml
# docker-compose.yml
services:
  autogen-api:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 🎯 预期结果

修复后，您应该看到：

```bash
$ docker compose ps
NAME           IMAGE                    COMMAND                  SERVICE       CREATED         STATUS                   PORTS
autogen-api    augment-work-autogen-api "python simple_api_se…"  autogen-api   2 minutes ago   Up 2 minutes (healthy)   0.0.0.0:8000->8000/tcp
autogen-web    augment-work-autogen-web "python simple_web_se…"  autogen-web   2 minutes ago   Up 2 minutes             0.0.0.0:8080->8080/tcp

$ docker compose logs autogen-web
autogen-web  | INFO:     Started server process [1]
autogen-web  | INFO:     Waiting for application startup.
autogen-web  | INFO:     Application startup complete.
autogen-web  | INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
```

## 📞 如果仍有问题

如果重新部署后仍有问题，请提供：

1. **容器状态**: `docker compose ps`
2. **完整日志**: `docker compose logs`
3. **镜像信息**: `docker images | grep augment-work`
4. **网络测试**: `curl` 命令的输出结果

这样我可以进一步协助您解决问题。
