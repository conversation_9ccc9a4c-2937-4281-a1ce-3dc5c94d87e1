"""
AutoGen编程工作流
实现三个agent的协作：代码编写、代码审查、代码优化

使用最新的AutoGen AgentChat框架
"""

import asyncio
from typing import List, Dict, Any, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo

# 导入配置模块
from config import WorkflowConfig, SystemMessages, TerminationConfig, get_config


class ProgrammingWorkflow:
    """编程工作流类，管理三个agent的协作"""

    def __init__(self, config_name: str = "default", config: Optional[WorkflowConfig] = None):
        """
        初始化工作流

        Args:
            config_name: 配置名称，如果提供了config参数则忽略
            config: 直接提供的配置对象
        """
        # 获取配置
        if config is not None:
            self.config = config
        else:
            self.config = get_config(config_name)

        # 创建模型客户端
        client_kwargs = {
            "model": self.config.model_type.value,
            "api_key": self.config.api_key,
        }

        # 如果指定了base_url，则添加到参数中
        if self.config.base_url:
            client_kwargs["base_url"] = self.config.base_url

        # 为 DeepSeek 模型添加 model_info
        if self.config.model_type.value.startswith("deepseek"):
            client_kwargs["model_info"] = ModelInfo(
                family="deepseek",
                vision=False,
                function_calling=True,
                json_output=True
            )

        self.model_client = OpenAIChatCompletionClient(**client_kwargs)

        # 初始化三个agent
        self._setup_agents()

        # 设置终止条件
        self._setup_termination_conditions()

        # 创建团队
        self._setup_team()
    
    def _setup_agents(self):
        """设置三个agent"""

        # 根据配置模式获取系统消息
        coder_msg, reviewer_msg, optimizer_msg = SystemMessages.get_messages_for_mode(self.config.mode)

        # Agent 1: 代码编写者
        self.coder_agent = AssistantAgent(
            name="coder",
            model_client=self.model_client,
            system_message=coder_msg,
            reflect_on_tool_use=True,
        )

        # Agent 2: 代码审查者
        self.reviewer_agent = AssistantAgent(
            name="reviewer",
            model_client=self.model_client,
            system_message=reviewer_msg,
        )

        # Agent 3: 代码优化者
        self.optimizer_agent = AssistantAgent(
            name="optimizer",
            model_client=self.model_client,
            system_message=optimizer_msg,
        )
    
    def _setup_termination_conditions(self):
        """设置终止条件"""
        # 根据配置获取终止关键词和最大消息数
        keywords = TerminationConfig.get_termination_keywords(self.config.mode)
        max_messages = self.config.max_messages

        # 创建终止条件
        text_conditions = [TextMentionTermination(keyword) for keyword in keywords]
        text_termination = text_conditions[0]
        for condition in text_conditions[1:]:
            text_termination = text_termination | condition

        self.termination_condition = (
            text_termination |
            MaxMessageTermination(max_messages)
        )
    
    def _setup_team(self):
        """设置团队"""
        self.team = RoundRobinGroupChat(
            participants=[self.coder_agent, self.reviewer_agent, self.optimizer_agent],
            termination_condition=self.termination_condition
        )
    
    async def run_programming_workflow(self, task: str, show_console: Optional[bool] = None):
        """
        运行编程工作流

        Args:
            task: 编程任务描述
            show_console: 是否在控制台显示过程，如果为None则使用配置中的设置

        Returns:
            TaskResult: 包含所有消息的任务结果
        """
        if show_console is None:
            show_console = self.config.show_console

        print(f"🚀 开始编程工作流 (模式: {self.config.mode.value})")
        print(f"🤖 模型: {self.config.model_type.value}")
        print(f"📝 任务: {task}")
        print("=" * 60)

        if show_console:
            # 使用Console显示实时过程
            result = await Console(self.team.run_stream(task=task))
        else:
            # 直接运行，不显示过程
            result = await self.team.run(task=task)

        print("=" * 60)
        print(f"✅ 工作流完成!")
        print(f"📊 总消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")

        return result
    
    async def reset_workflow(self):
        """重置工作流状态"""
        await self.team.reset()
        print("🔄 工作流已重置")
    
    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
        print("🔌 连接已关闭")


async def main():
    """主函数示例"""
    # 创建工作流实例
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务1：实现一个简单的数据结构
        task1 = """
        请实现一个Python类来表示一个简单的栈(Stack)数据结构。
        要求：
        1. 支持push(入栈)、pop(出栈)、peek(查看栈顶)、is_empty(判断是否为空)操作
        2. 包含适当的错误处理
        3. 添加必要的文档字符串
        4. 实现__str__方法用于打印栈的内容
        """
        
        result1 = await workflow.run_programming_workflow(task1)
        
        # 重置工作流准备下一个任务
        await workflow.reset_workflow()
        
        # 示例任务2：算法实现
        task2 = """
        请实现一个高效的快速排序算法。
        要求：
        1. 支持对整数列表进行排序
        2. 包含递归和迭代两种实现方式
        3. 添加性能测试代码
        4. 处理边界情况（空列表、单元素列表等）
        """
        
        result2 = await workflow.run_programming_workflow(task2)
        
    finally:
        # 关闭连接
        await workflow.close()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
