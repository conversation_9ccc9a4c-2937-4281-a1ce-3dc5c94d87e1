"""
AutoGen编程工作流配置文件
管理不同的配置选项和预设
"""

import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum


class ModelType(Enum):
    """支持的模型类型"""
    # OpenAI模型
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_4_TURBO = "gpt-4-turbo"
    GPT_35_TURBO = "gpt-3.5-turbo"

    # DeepSeek模型
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_REASONER = "deepseek-reasoner"


class WorkflowMode(Enum):
    """工作流模式"""
    STANDARD = "standard"  # 标准三agent模式
    FAST = "fast"         # 快速模式，减少迭代
    THOROUGH = "thorough" # 深度模式，更多迭代
    CUSTOM = "custom"     # 自定义模式


@dataclass
class AgentConfig:
    """Agent配置"""
    name: str
    system_message: str
    reflect_on_tool_use: bool = True
    model_client_stream: bool = False


@dataclass
class WorkflowConfig:
    """工作流配置"""
    model_type: ModelType = ModelType.DEEPSEEK_CHAT  # 默认使用DeepSeek
    api_key: Optional[str] = None
    base_url: Optional[str] = None  # API基础URL
    max_messages: int = 15
    mode: WorkflowMode = WorkflowMode.STANDARD
    show_console: bool = True
    enable_logging: bool = True
    log_level: str = "INFO"


@dataclass
class WebServiceConfig:
    """Web服务配置"""
    # API服务配置
    api_domain: str = "api.crawlonline.vip"
    api_port: int = 8000
    api_key: str = "sk-2d121f3bca354255a96d4c427cbe96d6"  # 自定义API密钥

    # Web服务配置
    web_domain: str = "chat.crawlonline.vip"
    web_port: int = 8080

    # 登录配置
    username: str = "admin"
    password: str = "autogen2024"

    # 安全配置
    secret_key: str = "autogen-web-secret-key-2024"
    session_timeout: int = 3600  # 会话超时时间（秒）

    # 服务配置
    debug: bool = False
    cors_origins: List[str] = None

    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = [
                f"https://{self.web_domain}",
                f"http://{self.web_domain}",
                f"https://{self.api_domain}",
                f"http://{self.api_domain}",
                "http://localhost:8080",
                "http://localhost:8000"
            ]


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.configs = self._load_default_configs()
    
    def _load_default_configs(self) -> Dict[str, WorkflowConfig]:
        """加载默认配置"""
        return {
            "default": WorkflowConfig(
                model_type=ModelType.DEEPSEEK_CHAT,
                base_url="https://api.deepseek.com"
            ),
            "fast": WorkflowConfig(
                model_type=ModelType.DEEPSEEK_CHAT,
                base_url="https://api.deepseek.com",
                max_messages=8,
                mode=WorkflowMode.FAST
            ),
            "thorough": WorkflowConfig(
                model_type=ModelType.DEEPSEEK_REASONER,
                base_url="https://api.deepseek.com",
                max_messages=25,
                mode=WorkflowMode.THOROUGH
            ),
            "economic": WorkflowConfig(
                model_type=ModelType.DEEPSEEK_CHAT,
                base_url="https://api.deepseek.com",
                max_messages=10,
                mode=WorkflowMode.FAST
            ),
            # OpenAI配置（备用）
            "openai": WorkflowConfig(
                model_type=ModelType.GPT_4O,
                base_url=None  # 使用默认OpenAI URL
            ),
            "openai-fast": WorkflowConfig(
                model_type=ModelType.GPT_4O_MINI,
                base_url=None,
                max_messages=8,
                mode=WorkflowMode.FAST
            )
        }
    
    def get_config(self, config_name: str = "default") -> WorkflowConfig:
        """获取配置"""
        if config_name not in self.configs:
            raise ValueError(f"未找到配置: {config_name}")

        config = self.configs[config_name]

        # 从环境变量获取API密钥
        if config.api_key is None:
            # 优先使用DeepSeek API密钥，然后是OpenAI密钥
            if config.model_type in [ModelType.DEEPSEEK_CHAT, ModelType.DEEPSEEK_REASONER]:
                config.api_key = (
                    os.getenv("DEEPSEEK_API_KEY") or
                    os.getenv("OPENAI_API_KEY") or
                    "sk-747b3a99f3b340e8a2a48592e2f6aaf1"  # 默认DeepSeek API密钥
                )
            else:
                config.api_key = os.getenv("OPENAI_API_KEY")

        return config
    
    def add_config(self, name: str, config: WorkflowConfig):
        """添加自定义配置"""
        self.configs[name] = config
    
    def list_configs(self) -> list:
        """列出所有可用配置"""
        return list(self.configs.keys())


class SystemMessages:
    """系统消息模板"""
    
    # 标准模式的系统消息
    STANDARD_CODER = """你是一个全栈编程专家，精通多种编程语言和技术栈。你的职责是：
1. 根据用户需求分析最适合的编程语言和技术栈
2. 编写高质量、符合最佳实践的代码
3. 支持的技术栈包括但不限于：
   - 前端：JavaScript/TypeScript, React, Vue, Angular, HTML/CSS
   - 后端：Python, Java, Node.js, Go, C#, PHP
   - 移动端：React Native, Flutter, Swift, Kotlin
   - 数据库：SQL, NoSQL, Redis
   - 云服务：AWS, Azure, GCP
4. 确保代码具有良好的可读性和结构
5. 添加必要的注释和文档
6. 遵循相应语言的编程最佳实践
7. 在代码完成后，说明代码的功能和使用方法

请始终提供完整、可运行的代码解决方案，并根据需求选择最合适的技术栈。"""

    STANDARD_REVIEWER = """你是一个经验丰富的多语言代码审查专家。你的职责是：
1. 仔细审查提供的代码，无论使用何种编程语言
2. 检查代码的正确性、效率和安全性
3. 识别潜在的bug、性能问题或安全漏洞
4. 评估代码的可读性和可维护性
5. 提出具体的改进建议
6. 检查是否遵循了相应语言的编程规范和最佳实践：
   - Python: PEP 8, 类型注解
   - JavaScript/TypeScript: ESLint, 现代ES6+语法
   - Java: Oracle编码规范, 设计模式
   - C#: Microsoft编码规范
   - 其他语言的相应标准
7. 评估架构设计和技术选型的合理性

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 风险评估

如果代码质量很好，请说"REVIEW_APPROVED"。"""

    STANDARD_OPTIMIZER = """你是一个多语言代码优化专家。你的职责是：
1. 基于原始代码和审查意见，重新优化代码
2. 提高代码的性能、可读性和可维护性
3. 修复审查中发现的问题
4. 优化算法和数据结构
5. 改进错误处理和边界条件处理
6. 应用相应语言的最佳实践和优化技巧：
   - Python: 列表推导、生成器、装饰器等
   - JavaScript: 异步编程、模块化、现代语法
   - Java: 流API、并发编程、内存优化
   - 其他语言的特定优化技术
7. 考虑跨平台兼容性和部署优化

请提供优化后的完整代码，并说明：
- 做了哪些优化
- 为什么这样优化
- 优化后的优势
- 性能提升预期

完成优化后，请说"OPTIMIZATION_COMPLETE"。"""

    # 快速模式的系统消息（更简洁）
    FAST_CODER = """你是一个高效的全栈程序员，精通多种编程语言。请根据需求选择最合适的技术栈，快速编写符合需求的代码，重点关注功能实现。"""

    FAST_REVIEWER = """你是多语言代码审查专家。请快速审查代码，重点关注关键问题。如果没有严重问题，请说"REVIEW_APPROVED"。"""

    FAST_OPTIMIZER = """你是多语言代码优化专家。请基于审查意见快速优化代码。完成后说"OPTIMIZATION_COMPLETE"。"""

    # 深度模式的系统消息（更详细）
    THOROUGH_CODER = """你是一个资深的全栈架构师和程序员，精通多种技术栈。你的职责是：
1. 深入理解用户需求，分析并选择最佳的技术栈和解决方案
2. 编写高质量、可扩展、可维护的代码
3. 考虑性能、安全性、可测试性、可扩展性等多个维度
4. 添加详细的文档和类型注解（如适用）
5. 遵循SOLID原则和相应的设计模式
6. 考虑错误处理和边界条件
7. 提供使用示例和测试建议
8. 考虑跨平台兼容性和部署策略
9. 应用现代开发最佳实践（CI/CD、容器化等）

请提供企业级质量的代码解决方案，并说明技术选型理由。"""

    THOROUGH_REVIEWER = """你是一个严格的高级多语言代码审查专家和架构师。你的职责是：
1. 从多个维度深入审查代码：功能性、性能、安全性、可维护性
2. 检查架构设计和技术选型是否合理
3. 评估代码的可扩展性和可测试性
4. 识别所有潜在的问题和改进点
5. 检查是否遵循了相应语言的最佳实践和设计原则
6. 评估代码的企业级应用适用性
7. 考虑跨平台兼容性和部署复杂度
8. 提供详细的改进建议和替代方案
9. 评估技术债务和长期维护成本

请提供全面的审查报告，只有在代码达到企业级标准时才说"REVIEW_APPROVED"。"""

    THOROUGH_OPTIMIZER = """你是一个资深的多语言代码优化和架构专家。你的职责是：
1. 基于审查意见进行全面的代码重构和优化
2. 优化算法复杂度和数据结构选择
3. 改进代码架构和设计模式应用
4. 增强错误处理和异常管理
5. 优化性能和内存使用
6. 提高代码的可读性、可维护性和可扩展性
7. 应用现代开发工具和框架
8. 考虑微服务架构和云原生部署
9. 实现自动化测试和监控
10. 确保代码符合企业级开发标准

请提供完全优化的代码，并详细说明所有改进点和技术选型理由。完成后说"OPTIMIZATION_COMPLETE"。"""

    @classmethod
    def get_messages_for_mode(cls, mode: WorkflowMode) -> tuple:
        """根据模式获取系统消息"""
        if mode == WorkflowMode.FAST:
            return (cls.FAST_CODER, cls.FAST_REVIEWER, cls.FAST_OPTIMIZER)
        elif mode == WorkflowMode.THOROUGH:
            return (cls.THOROUGH_CODER, cls.THOROUGH_REVIEWER, cls.THOROUGH_OPTIMIZER)
        else:  # STANDARD or CUSTOM
            return (cls.STANDARD_CODER, cls.STANDARD_REVIEWER, cls.STANDARD_OPTIMIZER)


class TerminationConfig:
    """终止条件配置"""
    
    @staticmethod
    def get_termination_keywords(mode: WorkflowMode) -> list:
        """根据模式获取终止关键词"""
        base_keywords = ["OPTIMIZATION_COMPLETE", "REVIEW_APPROVED"]
        
        if mode == WorkflowMode.FAST:
            return base_keywords + ["FAST_COMPLETE"]
        elif mode == WorkflowMode.THOROUGH:
            return base_keywords + ["THOROUGH_COMPLETE"]
        else:
            return base_keywords
    
    @staticmethod
    def get_max_messages(mode: WorkflowMode) -> int:
        """根据模式获取最大消息数"""
        if mode == WorkflowMode.FAST:
            return 8
        elif mode == WorkflowMode.THOROUGH:
            return 25
        else:
            return 15


# 全局配置管理器实例
config_manager = ConfigManager()

# 全局Web服务配置
web_service_config = WebServiceConfig()


def get_config(config_name: str = "default") -> WorkflowConfig:
    """便捷函数：获取配置"""
    return config_manager.get_config(config_name)


def get_web_config() -> WebServiceConfig:
    """便捷函数：获取Web服务配置"""
    return web_service_config


def list_available_configs() -> list:
    """便捷函数：列出可用配置"""
    return config_manager.list_configs()


# 示例：如何使用配置
if __name__ == "__main__":
    # 列出所有配置
    print("可用配置:")
    for config_name in list_available_configs():
        config = get_config(config_name)
        print(f"- {config_name}: {config.model_type.value}, 最大消息数: {config.max_messages}")
    
    # 获取特定配置
    fast_config = get_config("fast")
    print(f"\n快速配置: {fast_config}")
    
    # 获取系统消息
    coder_msg, reviewer_msg, optimizer_msg = SystemMessages.get_messages_for_mode(WorkflowMode.FAST)
    print(f"\n快速模式编码器消息: {coder_msg[:100]}...")
