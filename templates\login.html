{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                    <h2 class="card-title">AutoGen工作流</h2>
                    <p class="text-muted">请登录以继续使用</p>
                </div>

                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                </div>
                {% endif %}

                <form method="post" action="/login">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-2"></i>用户名
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required 
                               placeholder="请输入用户名" autocomplete="username">
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>密码
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required 
                               placeholder="请输入密码" autocomplete="current-password">
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>登录
                        </button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        安全登录 | 基于DeepSeek的AI编程助手
                    </small>
                </div>
            </div>
        </div>

        <!-- 功能介绍 -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-star text-warning me-2"></i>功能特色
                </h5>
                <div class="row">
                    <div class="col-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>智能代码生成</li>
                            <li><i class="fas fa-check text-success me-2"></i>代码审查优化</li>
                            <li><i class="fas fa-check text-success me-2"></i>多Agent协作</li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>API接口调用</li>
                            <li><i class="fas fa-check text-success me-2"></i>实时对话交互</li>
                            <li><i class="fas fa-check text-success me-2"></i>多种配置模式</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 自动聚焦到用户名输入框
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
    
    // 回车键快速登录
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const form = document.querySelector('form');
            if (form) {
                form.submit();
            }
        }
    });
</script>
{% endblock %}
