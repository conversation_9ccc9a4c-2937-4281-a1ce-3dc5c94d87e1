"""
完整工作流测试
使用DeepSeek API测试完整的三Agent编程工作流
"""

import asyncio
import os
import sys

# 设置DeepSeek API密钥
os.environ["DEEPSEEK_API_KEY"] = "***********************************"

try:
    from openai import OpenAI
    print("✅ OpenAI库可用")
except ImportError:
    print("❌ 请先安装OpenAI库: pip install --user openai")
    sys.exit(1)


class SimpleWorkflow:
    """简化的工作流实现，用于测试"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key="***********************************",
            base_url="https://api.deepseek.com"
        )
        
        self.agents = {
            "coder": {
                "name": "代码编写者",
                "system_message": """你是一个专业的Python程序员。请根据用户需求编写高质量的Python代码，包含注释和文档字符串。"""
            },
            "reviewer": {
                "name": "代码审查者", 
                "system_message": """你是一个代码审查专家。请仔细审查代码，指出问题并提出改进建议。如果代码质量很好，请说"REVIEW_APPROVED"。"""
            },
            "optimizer": {
                "name": "代码优化者",
                "system_message": """你是一个代码优化专家。请根据审查意见优化代码，提高性能和可读性。完成后说"OPTIMIZATION_COMPLETE"。"""
            }
        }
    
    async def run_agent(self, agent_type: str, messages: list) -> str:
        """运行单个agent"""
        agent = self.agents[agent_type]
        
        # 构建消息列表
        full_messages = [
            {"role": "system", "content": agent["system_message"]}
        ] + messages
        
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=full_messages,
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"❌ {agent['name']}执行失败: {e}"
    
    async def run_workflow(self, task: str) -> dict:
        """运行完整的三agent工作流"""
        print(f"🚀 开始执行任务: {task}")
        print("=" * 60)
        
        messages = [{"role": "user", "content": task}]
        results = {}
        
        # 第一轮：代码编写
        print("👨‍💻 Agent 1: 代码编写者")
        print("-" * 30)
        coder_response = await self.run_agent("coder", messages)
        print(f"📝 响应: {coder_response[:200]}...")
        results["coder"] = coder_response
        
        # 添加到消息历史
        messages.append({"role": "assistant", "content": coder_response})
        
        # 第二轮：代码审查
        print("\n🔍 Agent 2: 代码审查者")
        print("-" * 30)
        reviewer_response = await self.run_agent("reviewer", messages)
        print(f"📝 响应: {reviewer_response[:200]}...")
        results["reviewer"] = reviewer_response
        
        # 添加到消息历史
        messages.append({"role": "assistant", "content": reviewer_response})
        
        # 第三轮：代码优化
        print("\n⚡ Agent 3: 代码优化者")
        print("-" * 30)
        optimizer_response = await self.run_agent("optimizer", messages)
        print(f"📝 响应: {optimizer_response[:200]}...")
        results["optimizer"] = optimizer_response
        
        print("\n" + "=" * 60)
        print("✅ 工作流完成!")
        
        return results


async def test_simple_task():
    """测试简单任务"""
    print("🧪 测试1: 简单编程任务")
    print("=" * 50)
    
    workflow = SimpleWorkflow()
    task = "请实现一个Python函数来计算斐波那契数列的第n项。"
    
    try:
        results = await workflow.run_workflow(task)
        
        # 检查结果
        success_indicators = [
            "def " in results.get("coder", ""),
            len(results.get("reviewer", "")) > 50,
            len(results.get("optimizer", "")) > 50
        ]
        
        success_rate = sum(success_indicators) / len(success_indicators)
        print(f"\n📊 成功率: {success_rate*100:.1f}%")
        
        return success_rate > 0.5
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_complex_task():
    """测试复杂任务"""
    print("\n🧪 测试2: 复杂编程任务")
    print("=" * 50)
    
    workflow = SimpleWorkflow()
    task = """
    请实现一个Python类来管理学生信息系统。
    要求：
    1. 支持添加、删除、查找学生
    2. 计算平均成绩
    3. 包含错误处理
    4. 添加文档字符串
    """
    
    try:
        results = await workflow.run_workflow(task)
        
        # 检查结果质量
        coder_result = results.get("coder", "")
        quality_indicators = [
            "class " in coder_result,
            "def " in coder_result,
            "添加" in coder_result or "add" in coder_result.lower(),
            "删除" in coder_result or "delete" in coder_result.lower(),
            "查找" in coder_result or "find" in coder_result.lower()
        ]
        
        quality_score = sum(quality_indicators) / len(quality_indicators)
        print(f"\n📊 质量评分: {quality_score*100:.1f}%")
        
        return quality_score > 0.6
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_workflow_interaction():
    """测试agent间的交互"""
    print("\n🧪 测试3: Agent交互质量")
    print("=" * 50)
    
    workflow = SimpleWorkflow()
    task = "实现一个简单的栈数据结构，包含push、pop、peek操作。"
    
    try:
        results = await workflow.run_workflow(task)
        
        # 检查交互质量
        reviewer_mentions_code = any(
            keyword in results.get("reviewer", "").lower() 
            for keyword in ["代码", "函数", "class", "def", "实现"]
        )
        
        optimizer_mentions_review = any(
            keyword in results.get("optimizer", "").lower()
            for keyword in ["建议", "改进", "优化", "修改", "根据"]
        )
        
        interaction_score = (reviewer_mentions_code + optimizer_mentions_review) / 2
        print(f"\n📊 交互质量: {interaction_score*100:.1f}%")
        
        return interaction_score > 0.5
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 DeepSeek三Agent编程工作流测试")
    print("=" * 60)
    print(f"🔑 使用API密钥: ***********************************")
    print(f"🌐 API端点: https://api.deepseek.com")
    print(f"🤖 模型: deepseek-chat")
    
    # 运行测试
    tests = [
        ("简单任务", test_simple_task),
        ("复杂任务", test_complex_task),
        ("交互质量", test_workflow_interaction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = await test_func()
            results.append((test_name, "✅ 通过" if success else "⚠️ 部分通过"))
        except Exception as e:
            results.append((test_name, f"❌ 失败: {str(e)}"))
            print(f"❌ {test_name} 发生异常: {str(e)}")
    
    # 打印测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    for test_name, status in results:
        print(f"{test_name}: {status}")
    
    # 统计
    passed = sum(1 for _, status in results if "✅" in status)
    partial = sum(1 for _, status in results if "⚠️" in status)
    total = len(results)
    
    print(f"\n📈 结果统计:")
    print(f"  完全通过: {passed}/{total}")
    print(f"  部分通过: {partial}/{total}")
    print(f"  总体成功率: {(passed + partial*0.5)/total*100:.1f}%")
    
    if passed + partial > 0:
        print("\n🎉 DeepSeek工作流配置成功!")
        print("💡 您现在可以使用完整的AutoGen编程工作流了。")
        print("\n📋 下一步:")
        print("  1. 运行: python run.py")
        print("  2. 或者: python demo.py")
        print("  3. 或者: make run")
    else:
        print("\n❌ 工作流测试失败，请检查配置。")


if __name__ == "__main__":
    asyncio.run(main())
