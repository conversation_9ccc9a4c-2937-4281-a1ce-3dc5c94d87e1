{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h1 class="card-title">
                    <i class="fas fa-tachometer-alt text-primary me-3"></i>
                    欢迎使用AutoGen编程工作流
                </h1>
                <p class="card-text lead">
                    基于DeepSeek的多Agent编程协作平台，为您提供智能代码生成、审查和优化服务。
                </p>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-user text-info me-2"></i>
                            <strong>当前用户：</strong>{{ username }}
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-clock text-warning me-2"></i>
                            <strong>登录时间：</strong><span id="loginTime"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-server text-success me-2"></i>
                            <strong>API域名：</strong>{{ api_domain }}:{{ api_port }}
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-robot text-primary me-2"></i>
                            <strong>服务状态：</strong>
                            <span class="badge bg-success">运行中</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 对话页入口 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-comments fa-4x text-primary"></i>
                </div>
                <h3 class="card-title">智能对话</h3>
                <p class="card-text">
                    与AI编程助手进行实时对话，获得代码生成、审查和优化建议。
                    支持多种配置模式，满足不同复杂度的编程需求。
                </p>
                <div class="mb-3">
                    <span class="badge bg-info me-2">代码生成</span>
                    <span class="badge bg-success me-2">代码审查</span>
                    <span class="badge bg-warning me-2">代码优化</span>
                </div>
                <a href="/chat" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>开始对话
                </a>
            </div>
        </div>
    </div>

    <!-- API接入页入口 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="fas fa-code fa-4x text-success"></i>
                </div>
                <h3 class="card-title">API接入</h3>
                <p class="card-text">
                    查看API接入文档，获取接口调用示例和参数说明。
                    支持RESTful API调用，轻松集成到您的应用中。
                </p>
                <div class="mb-3">
                    <span class="badge bg-primary me-2">RESTful API</span>
                    <span class="badge bg-secondary me-2">JSON格式</span>
                    <span class="badge bg-dark me-2">安全认证</span>
                </div>
                <a href="/api-docs" class="btn btn-success btn-lg">
                    <i class="fas fa-book me-2"></i>查看文档
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 功能统计 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>平台特性
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="p-3">
                            <i class="fas fa-robot fa-2x text-primary mb-2"></i>
                            <h6>多Agent协作</h6>
                            <small class="text-muted">代码编写、审查、优化三重保障</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3">
                            <i class="fas fa-brain fa-2x text-info mb-2"></i>
                            <h6>DeepSeek驱动</h6>
                            <small class="text-muted">基于最新DeepSeek模型技术</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3">
                            <i class="fas fa-cogs fa-2x text-warning mb-2"></i>
                            <h6>多种配置</h6>
                            <small class="text-muted">快速、标准、深度多种模式</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3">
                            <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                            <h6>安全可靠</h6>
                            <small class="text-muted">API密钥认证，数据安全保护</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速开始指南 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>快速开始
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-comments text-primary me-2"></i>Web对话方式</h6>
                        <ol class="small">
                            <li>点击"开始对话"按钮</li>
                            <li>选择合适的配置模式</li>
                            <li>输入编程任务描述</li>
                            <li>查看AI生成的代码结果</li>
                        </ol>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-code text-success me-2"></i>API调用方式</h6>
                        <ol class="small">
                            <li>查看API接入文档</li>
                            <li>获取API密钥和端点</li>
                            <li>构建HTTP请求</li>
                            <li>处理返回的JSON结果</li>
                        </ol>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-cog text-warning me-2"></i>配置说明</h6>
                        <ul class="small">
                            <li><strong>default:</strong> 标准模式，平衡质量和速度</li>
                            <li><strong>fast:</strong> 快速模式，适合简单任务</li>
                            <li><strong>thorough:</strong> 深度模式，复杂逻辑推理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 显示登录时间
    document.addEventListener('DOMContentLoaded', function() {
        const now = new Date();
        document.getElementById('loginTime').textContent = now.toLocaleString('zh-CN');
    });
    
    // 定期检查服务状态
    function checkServiceStatus() {
        fetch('/api/configs')
            .then(response => response.ok)
            .then(isOk => {
                const statusBadge = document.querySelector('.badge.bg-success');
                if (statusBadge) {
                    statusBadge.textContent = isOk ? '运行中' : '异常';
                    statusBadge.className = isOk ? 'badge bg-success' : 'badge bg-danger';
                }
            })
            .catch(() => {
                const statusBadge = document.querySelector('.badge');
                if (statusBadge) {
                    statusBadge.textContent = '连接异常';
                    statusBadge.className = 'badge bg-danger';
                }
            });
    }
    
    // 每30秒检查一次状态
    setInterval(checkServiceStatus, 30000);
    checkServiceStatus(); // 立即检查一次
</script>
{% endblock %}
