<!DOCTYPE html>
<html>
<head>
    <title>代码块测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
</head>
<body>
    <div id="output"></div>
    
    <script>
        // 测试内容
        const testContent = `# JavaScript 排序算法

这是一个测试：

\`\`\`javascript
function quickSort(arr) {
    if (arr.length <= 1) return arr;
    const pivot = arr[Math.floor(arr.length / 2)];
    const left = arr.filter(x => x < pivot);
    const right = arr.filter(x => x > pivot);
    return [...quickSort(left), pivot, ...quickSort(right)];
}
\`\`\`

另一个代码块：

\`\`\`python
def bubble_sort(arr):
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr
\`\`\``;

        // 复制formatContent函数
        function getFilenameFromLanguage(lang) {
            const extensions = {
                'javascript': 'script.js',
                'python': 'script.py',
                'java': 'Main.java',
                'cpp': 'main.cpp',
                'c': 'main.c',
                'html': 'index.html',
                'css': 'style.css',
                'sql': 'query.sql',
                'bash': 'script.sh',
                'json': 'data.json',
                'xml': 'data.xml',
                'yaml': 'config.yaml',
                'typescript': 'script.ts',
                'php': 'script.php',
                'ruby': 'script.rb',
                'go': 'main.go',
                'rust': 'main.rs',
                'swift': 'main.swift',
                'kotlin': 'Main.kt',
                'scala': 'Main.scala',
                'r': 'script.R',
                'matlab': 'script.m',
                'perl': 'script.pl',
                'lua': 'script.lua',
                'dart': 'main.dart',
                'text': 'file.txt'
            };
            return extensions[lang] || `file.${lang}`;
        }

        function formatContent(content) {
            // 保存代码块的占位符映射
            const codeBlocks = {};
            let codeBlockIndex = 0;

            // 首先提取并替换代码块为占位符，避免被 marked.js 处理
            content = content.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
                const lang = language || 'text';
                const filename = getFilenameFromLanguage(lang);
                const codeId = 'code-' + Math.random().toString(36).substr(2, 9);
                const placeholder = `<!--CODE_BLOCK_${codeBlockIndex}-->`;
                
                codeBlocks[placeholder] = `
                    <div class="code-block">
                        <div class="code-header">
                            <span><i class="fas fa-file-code me-1"></i>${filename}</span>
                            <div class="code-actions">
                                <button onclick="copyCode('${codeId}')" title="复制代码">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                                <button onclick="downloadCode('${codeId}', '${filename}')" title="下载文件">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        <pre class="line-numbers"><code id="${codeId}" class="language-${lang}">${code.trim()}</code></pre>
                    </div>
                `;
                
                codeBlockIndex++;
                return placeholder;
            });

            // 处理行内代码（在 marked.js 之前）
            content = content.replace(/`([^`\n]+)`/g, '<code class="language-text">$1</code>');

            // 使用 marked.js 处理其他 Markdown
            try {
                content = marked.parse(content);
            } catch (e) {
                console.warn('Markdown parsing failed:', e);
            }

            // 最后恢复代码块（HTML注释不会被marked.js处理）
            Object.keys(codeBlocks).forEach(placeholder => {
                // HTML注释格式的占位符不需要转义
                content = content.replace(new RegExp(placeholder, 'g'), codeBlocks[placeholder]);
            });

            return content;
        }

        // 测试
        const output = document.getElementById('output');
        output.innerHTML = formatContent(testContent);
        
        // 高亮代码
        Prism.highlightAll();
        
        console.log('测试完成');
    </script>
</body>
</html>
