

services:
  autogen-api:
    build: .
    container_name: autogen-api
    ports:
      - "8000:8000"
    environment:
      - DEEPSEEK_API_KEY=***********************************
    command: ["python", "api_server.py"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  autogen-web:
    build: .
    container_name: autogen-web
    ports:
      - "8080:8080"
    environment:
      - DEEPSEEK_API_KEY=***********************************
    command: ["python", "web_server.py"]
    restart: unless-stopped
    depends_on:
      - autogen-api

networks:
  default:
    name: autogen-network
