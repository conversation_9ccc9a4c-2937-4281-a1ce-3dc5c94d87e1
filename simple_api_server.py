#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoGen API服务器 - 简化版
提供编程任务API接口
"""

import os
import sys
import time
import uuid
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from config import get_web_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 获取配置
web_config = get_web_config()

# 创建FastAPI应用
app = FastAPI(
    title="AutoGen API服务",
    description="智能编程助手API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求和响应模型
class TaskRequest(BaseModel):
    task: str = Field(..., description="编程任务描述")
    config_name: str = Field(default="default", description="配置名称")
    show_console: bool = Field(default=False, description="是否显示控制台输出")

class TaskResponse(BaseModel):
    success: bool
    task_id: str
    result: Optional[Dict[str, Any]] = None
    execution_time: float
    message_count: int
    stop_reason: str

# API密钥验证
async def verify_api_key(request: Request):
    """验证API密钥"""
    api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization", "").replace("Bearer ", "")
    
    if not api_key:
        raise HTTPException(status_code=401, detail="缺少API密钥")
    
    if api_key != web_config.api_key:
        raise HTTPException(status_code=403, detail="无效的API密钥")
    
    return api_key

# 智能代码生成函数
def generate_smart_code(task: str) -> list:
    """根据任务智能生成代码"""
    task_lower = task.lower()
    
    # 检测任务类型并生成相应代码
    if "排序" in task and ("javascript" in task_lower or "js" in task_lower):
        return generate_sorting_code(task)
    elif "网页" in task or "html" in task_lower or "css" in task_lower or "javascript" in task_lower:
        return generate_web_code(task)
    elif "python" in task_lower:
        return generate_python_code(task)
    elif "java" in task_lower and "script" not in task_lower:
        return generate_java_code(task)
    elif "数据库" in task or "sql" in task_lower:
        return generate_database_code(task)
    else:
        return generate_general_code(task)

def generate_web_code(task: str) -> list:
    """生成Web代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您创建了一个现代化的Web解决方案：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代网页应用</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }}
        
        .card {{
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }}
        
        .card:hover {{
            transform: translateY(-5px);
        }}
        
        .btn {{
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .btn:hover {{
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>现代网页应用</h1>
            <p>响应式设计，现代化界面</p>
        </div>
        
        <div class="card">
            <h2>功能特点</h2>
            <p>这是一个现代化的网页模板，包含了响应式设计和交互效果。</p>
            <button class="btn" onclick="showAlert()">点击测试</button>
        </div>
    </div>
    
    <script>
        function showAlert() {{
            alert('网页功能正常！');
        }}
    </script>
</body>
</html>
```

## 功能特点：
1. 现代化设计 - 使用渐变背景和卡片布局
2. 响应式布局 - 适配不同屏幕尺寸  
3. 交互效果 - 悬停动画和按钮效果
4. 易于定制 - 清晰的CSS结构，便于修改

## 使用方法：
1. 将代码保存为 .html 文件
2. 在浏览器中打开即可查看效果""",
            "type": "text"
        }
    ]

def generate_sorting_code(task: str) -> list:
    """生成JavaScript排序算法代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您实现了JavaScript排序算法：

```javascript
// 快速排序算法
function quickSort(arr) {{
    if (arr.length <= 1) return arr;
    
    const pivot = arr[Math.floor(arr.length / 2)];
    const left = arr.filter(x => x < pivot);
    const right = arr.filter(x => x > pivot);
    const equal = arr.filter(x => x === pivot);
    
    return [...quickSort(left), ...equal, ...quickSort(right)];
}}

// 使用示例
const numbers = [64, 34, 25, 12, 22, 11, 90];
console.log('原始数组:', numbers);
console.log('排序结果:', quickSort(numbers));
```

## 算法特点：
1. 时间复杂度：平均 O(n log n)
2. 空间复杂度：O(log n)
3. 适用于大数据集排序
4. 实现简洁易懂""",
            "type": "text"
        }
    ]

def generate_python_code(task: str) -> list:
    """生成Python代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您编写了Python解决方案：

```python
def solve_task(data):
    \"\"\"
    Python解决方案
    \"\"\"
    print(f"处理任务: {{data}}")
    
    # 核心逻辑
    result = f"处理完成: {{data}}"
    return result

# 使用示例
if __name__ == "__main__":
    result = solve_task("测试数据")
    print(result)
```

## 代码特点：
1. 结构清晰 - 函数式设计
2. 易于扩展 - 模块化结构
3. 包含测试 - 完整示例""",
            "type": "text"
        }
    ]

def generate_java_code(task: str) -> list:
    """生成Java代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您编写了Java解决方案：

```java
public class Solution {{
    public static void main(String[] args) {{
        Solution solution = new Solution();
        String result = solution.solve("测试数据");
        System.out.println("结果: " + result);
    }}
    
    public String solve(String input) {{
        System.out.println("开始处理: " + input);
        return "处理完成: " + input;
    }}
}}
```

## 代码特点：
1. 标准Java结构
2. 面向对象设计
3. 包含测试方法""",
            "type": "text"
        }
    ]

def generate_database_code(task: str) -> list:
    """生成数据库代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您编写了数据库解决方案：

```sql
-- 示例查询
SELECT * FROM users 
WHERE created_at >= NOW() - INTERVAL 30 DAY
ORDER BY created_at DESC;
```

## 功能特点：
1. 标准SQL语法
2. 高效查询设计
3. 易于维护""",
            "type": "text"
        }
    ]

def generate_general_code(task: str) -> list:
    """生成通用代码"""
    return [
        {
            "source": "coder",
            "content": f"""# 编程专家

根据您的需求："{task}"

我为您提供了一个通用的解决方案框架：

```python
def solve_task(task_description: str) -> str:
    print(f"正在处理任务: {{task_description}}")
    return "任务处理完成"

result = solve_task("{task}")
print(f"结果: {{result}}")
```

这是一个可扩展的解决方案框架，可以根据具体需求进行定制。""",
            "type": "text"
        }
    ]

# API路由
@app.post("/api/v1/programming-task", response_model=TaskResponse)
async def execute_programming_task(
    request: TaskRequest,
    api_key: str = Depends(verify_api_key)
) -> TaskResponse:
    """执行编程任务"""
    task_id = str(uuid.uuid4())
    start_time = time.time()

    logger.info(f"开始执行任务 {task_id}: {request.task[:100]}...")

    try:
        # 模拟处理时间
        await asyncio.sleep(1)

        # 智能生成代码
        messages = generate_smart_code(request.task)

        execution_time = time.time() - start_time

        response_data = {
            "messages": messages,
            "stop_reason": "TASK_COMPLETE",
            "total_messages": len(messages)
        }

        logger.info(f"任务 {task_id} 执行成功，耗时 {execution_time:.2f}秒")

        return TaskResponse(
            success=True,
            task_id=task_id,
            result=response_data,
            execution_time=execution_time,
            message_count=len(messages),
            stop_reason="TASK_COMPLETE"
        )

    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"任务执行失败: {str(e)}"
        logger.error(f"任务 {task_id} 失败: {error_msg}")

        return TaskResponse(
            success=False,
            task_id=task_id,
            result={"error": error_msg},
            execution_time=execution_time,
            message_count=0,
            stop_reason="ERROR"
        )

@app.get("/api/v1/configs")
async def get_available_configs(api_key: str = Depends(verify_api_key)):
    """获取可用配置列表"""
    configs = [
        {
            "name": "default",
            "model_type": "deepseek-chat",
            "mode": "standard",
            "max_messages": 15,
            "description": "deepseek-chat - standard模式"
        },
        {
            "name": "fast",
            "model_type": "deepseek-chat",
            "mode": "fast",
            "max_messages": 8,
            "description": "deepseek-chat - fast模式"
        }
    ]

    return {"configs": configs}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

def run_api_server():
    """运行API服务器"""
    logger.info(f"🚀 启动AutoGen API服务 (演示版)")
    logger.info(f"📡 API域名: {web_config.api_domain}")
    logger.info(f"🔑 API密钥: {web_config.api_key}")
    logger.info(f"📚 API文档: http://{web_config.api_domain}:{web_config.api_port}/docs")

    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=web_config.api_port,
        reload=web_config.debug,
        log_level="info"
    )

if __name__ == "__main__":
    run_api_server()
