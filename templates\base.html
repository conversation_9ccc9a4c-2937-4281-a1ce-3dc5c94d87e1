<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AutoGen工作流{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Prism.js for code highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar {
            z-index: 1030 !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .dropdown-menu {
            z-index: 1050 !important;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: none;
            border-radius: 8px;
            background: rgba(255,255,255,0.98);
            backdrop-filter: blur(10px);
            min-width: 180px;
        }

        .dropdown-item {
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .dropdown-item.text-danger:hover {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            position: relative;
            z-index: 1;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background: linear-gradient(45deg, #667eea, #764ba2);
          
            margin-left: auto;
        }
        
        .message.assistant {
            background: #e9ecef;
            color: #333;
        }
        
        .message.system {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #667eea;
        }
        
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        code {
            padding: 2px 6px;
            border-radius: 4px;
            color: #d63384;
        }
        
        .footer {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255,255,255,0.2);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-robot me-2"></i>AutoGen工作流
            </a>

            {% if request.session.get('logged_in') %}
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user me-1"></i>{{ request.session.get('username', 'User') }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/dashboard"><i class="fas fa-tachometer-alt me-2"></i>控制台</a></li>
                        <li><a class="dropdown-item" href="/chat"><i class="fas fa-comments me-2"></i>对话</a></li>
                        <li><a class="dropdown-item" href="/api-docs"><i class="fas fa-code me-2"></i>API文档</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出</a></li>
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container" style="margin-top: 100px;">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer mt-5 py-3">
        <div class="container text-center text-white">
            <small>
                &copy; 2024 AutoGen编程工作流 | 
                基于 <a href="https://microsoft.github.io/autogen/" class="text-white">AutoGen</a> 和 
                <a href="https://www.deepseek.com/" class="text-white">DeepSeek</a> 构建
            </small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for code highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // 全局JavaScript函数
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // 自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        function formatCode(text) {
            // 简单的代码格式化
            return text.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, lang, code) {
                return `<pre><code class="language-${lang || 'python'}">${code.trim()}</code></pre>`;
            });
        }

        // 确保下拉菜单正确显示
        document.addEventListener('DOMContentLoaded', function() {
            // 修复下拉菜单层级问题
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.style.zIndex = '1050';
            });

            // 处理下拉菜单点击
            const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const menu = this.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {
                        // 确保菜单在最顶层
                        menu.style.zIndex = '1050';
                        menu.style.position = 'absolute';
                    }
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
