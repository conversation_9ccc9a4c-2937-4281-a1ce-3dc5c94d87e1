#!/usr/bin/env python3
"""
AutoGen编程工作流服务启动脚本
快速启动API和Web服务
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import get_web_config


async def start_api_service():
    """启动API服务"""
    print("🚀 启动API服务...")
    
    try:
        from api_server import run_api_server
        run_api_server()
    except ImportError as e:
        print(f"❌ 导入API服务失败: {e}")
        print("请确保已安装 fastapi 和 uvicorn")
        return False
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        return False


async def start_web_service():
    """启动Web服务"""
    print("🌐 启动Web服务...")
    
    try:
        from web_server import run_web_server
        run_web_server()
    except ImportError as e:
        print(f"❌ 导入Web服务失败: {e}")
        print("请确保已安装 fastapi、uvicorn 和 jinja2")
        return False
    except Exception as e:
        print(f"❌ Web服务启动失败: {e}")
        return False


def check_basic_dependencies():
    """检查基础依赖"""
    required_modules = [
        ("fastapi", "FastAPI web框架"),
        ("uvicorn", "ASGI服务器"),
        ("jinja2", "模板引擎"),
        ("openai", "OpenAI客户端")
    ]
    
    missing = []
    for module, desc in required_modules:
        try:
            __import__(module)
            print(f"✅ {desc}")
        except ImportError:
            missing.append((module, desc))
            print(f"❌ {desc}")
    
    if missing:
        print("\n缺少以下依赖:")
        for module, desc in missing:
            print(f"  - {module}: {desc}")
        print("\n请运行以下命令安装:")
        modules = " ".join([m[0] for m in missing])
        print(f"  pip install --user {modules}")
        return False
    
    return True


def main():
    """主函数"""
    print("🚀 AutoGen编程工作流服务启动器")
    print("=" * 50)
    
    # 获取配置
    config = get_web_config()
    
    print(f"📡 API域名: {config.api_domain}:{config.api_port}")
    print(f"🌐 Web域名: {config.web_domain}:{config.web_port}")
    print(f"🔑 API密钥: {config.api_key}")
    print(f"👤 登录用户: {config.username}")
    print(f"🔐 登录密码: {config.password}")
    print("=" * 50)
    
    # 检查依赖
    if not check_basic_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺少的依赖")
        return 1
    
    # 选择启动模式
    print("\n请选择启动模式:")
    print("1. 启动API服务 (端口 8000)")
    print("2. 启动Web服务 (端口 8080)")
    print("3. 同时启动两个服务")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 启动API服务...")
            os.system(f"python api_server.py")
            
        elif choice == "2":
            print("\n🌐 启动Web服务...")
            os.system(f"python web_server.py")
            
        elif choice == "3":
            print("\n🚀 同时启动API和Web服务...")
            print("注意: 这将在后台启动API服务，前台运行Web服务")
            print("按 Ctrl+C 可以停止服务")
            
            # 使用部署脚本
            os.system(f"python deploy.py --mode all")
            
        elif choice == "4":
            print("👋 再见!")
            return 0
            
        else:
            print("❌ 无效选择")
            return 1
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
