# AutoGen编程工作流 Makefile
# 提供常用的项目管理命令

.PHONY: help install test demo clean lint format check setup run

# 默认目标
help:
	@echo "🚀 AutoGen编程工作流 - 可用命令:"
	@echo ""
	@echo "📦 安装和设置:"
	@echo "  make install     - 安装所有依赖"
	@echo "  make setup       - 完整环境设置"
	@echo ""
	@echo "🧪 测试和验证:"
	@echo "  make test        - 运行完整测试"
	@echo "  make test-deepseek - 测试DeepSeek API"
	@echo "  make check       - 检查代码质量"
	@echo "  make lint        - 代码检查"
	@echo ""
	@echo "🎯 运行和演示:"
	@echo "  make run         - 启动交互模式"
	@echo "  make start       - 启动服务选择器"
	@echo "  make start-web   - 启动Web服务"
	@echo "  make start-api   - 启动API服务"
	@echo "  make start-all   - 启动所有服务"
	@echo "  make demo        - 运行演示"
	@echo "  make examples    - 运行示例"
	@echo ""
	@echo "🔧 开发工具:"
	@echo "  make format      - 格式化代码"
	@echo "  make clean       - 清理临时文件"
	@echo ""
	@echo "📋 信息:"
	@echo "  make info        - 显示项目信息"
	@echo "  make deps        - 显示依赖信息"

# 安装依赖
install:
	@echo "📦 安装依赖..."
	pip install --upgrade pip
	pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# 完整环境设置
setup: install
	@echo "🔧 设置开发环境..."
	@if [ ! -f .env ]; then \
		echo "📝 创建.env模板文件..."; \
		echo "OPENAI_API_KEY=your-api-key-here" > .env; \
		echo "⚠️  请编辑.env文件设置您的API密钥"; \
	fi
	@echo "✅ 环境设置完成"

# 运行测试
test:
	@echo "🧪 运行测试..."
	python test_complete_workflow.py
	@echo "✅ 测试完成"

# 测试DeepSeek API
test-deepseek:
	@echo "🧪 测试DeepSeek API..."
	python simple_deepseek_test.py
	@echo "✅ DeepSeek测试完成"

# 代码质量检查
check: lint
	@echo "🔍 运行完整检查..."
	@echo "✅ 检查完成"

# 代码检查
lint:
	@echo "🔍 检查代码质量..."
	@if command -v flake8 >/dev/null 2>&1; then \
		echo "运行 flake8..."; \
		flake8 *.py --max-line-length=100 --ignore=E501,W503; \
	else \
		echo "⚠️  flake8 未安装，跳过检查"; \
	fi
	@if command -v mypy >/dev/null 2>&1; then \
		echo "运行 mypy..."; \
		mypy *.py --ignore-missing-imports; \
	else \
		echo "⚠️  mypy 未安装，跳过类型检查"; \
	fi

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	@if command -v black >/dev/null 2>&1; then \
		echo "运行 black..."; \
		black *.py --line-length=100; \
		echo "✅ 代码格式化完成"; \
	else \
		echo "⚠️  black 未安装，请运行: pip install black"; \
	fi

# 启动交互模式
run:
	@echo "🚀 启动AutoGen编程工作流..."
	python run.py

# 启动Web服务
start-web:
	@echo "🌐 启动Web服务..."
	python simple_web_server.py

# 启动API服务
start-api:
	@echo "📡 启动API服务..."
	python simple_api_server.py

# 启动所有服务
start-all:
	@echo "🚀 启动所有服务..."
	python deploy.py --mode all

# 启动服务选择器
start:
	@echo "🚀 启动服务选择器..."
	python start_services.py

# 运行演示
demo:
	@echo "🎯 运行演示..."
	python demo.py

# 运行示例
examples:
	@echo "📚 运行示例..."
	python workflow_examples.py

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name ".coverage" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	@echo "✅ 清理完成"

# 显示项目信息
info:
	@echo "📋 项目信息:"
	@echo "  名称: AutoGen编程工作流"
	@echo "  版本: 1.0.0"
	@echo "  Python版本: $(shell python --version)"
	@echo "  工作目录: $(shell pwd)"
	@echo ""
	@echo "📁 项目文件:"
	@ls -la *.py *.md *.txt 2>/dev/null || true
	@echo ""
	@echo "⚙️  环境变量:"
	@if [ -n "$$OPENAI_API_KEY" ]; then \
		echo "  OPENAI_API_KEY: 已设置"; \
	else \
		echo "  OPENAI_API_KEY: 未设置 ⚠️"; \
	fi

# 显示依赖信息
deps:
	@echo "📦 已安装的依赖:"
	@pip list | grep -E "(autogen|openai)" || echo "  未找到相关依赖"
	@echo ""
	@echo "📋 requirements.txt 中的依赖:"
	@cat requirements.txt | grep -v "^#" | grep -v "^$$"

# 快速任务 - 实现排序算法
quick-sort:
	@echo "🎯 快速任务: 实现排序算法"
	python run.py -t "实现一个快速排序算法，包含递归和迭代两种版本，添加性能测试" -c fast

# 快速任务 - 创建计算器
quick-calc:
	@echo "🎯 快速任务: 创建计算器"
	python run.py -t "创建一个支持基本四则运算和科学计算的计算器类" -c default

# 快速任务 - 数据结构
quick-ds:
	@echo "🎯 快速任务: 数据结构实现"
	python run.py -t "实现一个二叉搜索树，支持插入、删除、搜索和遍历操作" -c thorough

# 安装开发依赖
install-dev: install
	@echo "📦 安装开发依赖..."
	pip install black flake8 mypy pytest pytest-asyncio
	@echo "✅ 开发依赖安装完成"

# 运行完整的CI检查
ci: clean install-dev lint test
	@echo "🎉 CI检查全部通过!"

# 创建发布包
package: clean format
	@echo "📦 创建发布包..."
	@mkdir -p dist
	@tar -czf dist/autogen-programming-workflow.tar.gz \
		*.py *.md *.txt Makefile \
		--exclude=".env" \
		--exclude="dist" \
		--exclude="__pycache__"
	@echo "✅ 发布包已创建: dist/autogen-programming-workflow.tar.gz"

# 显示使用统计
stats:
	@echo "📊 项目统计:"
	@echo "  Python文件数: $(shell find . -name "*.py" | wc -l)"
	@echo "  代码行数: $(shell find . -name "*.py" -exec wc -l {} + | tail -1 | awk '{print $$1}')"
	@echo "  文档文件数: $(shell find . -name "*.md" | wc -l)"
	@echo "  总文件大小: $(shell du -sh . | cut -f1)"
