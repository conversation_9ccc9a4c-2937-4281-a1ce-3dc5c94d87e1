{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-code text-success me-2"></i>API接入文档
                </h3>
                <p class="mb-0 text-muted">AutoGen编程工作流RESTful API使用指南</p>
            </div>
            <div class="card-body">
                <!-- API基本信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="fas fa-server me-2"></i>服务信息</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>API域名：</strong></td>
                                <td><code>{{ api_domain }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>端口：</strong></td>
                                <td><code>{{ api_port }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>协议：</strong></td>
                                <td><code>HTTPS/HTTP</code></td>
                            </tr>
                            <tr>
                                <td><strong>格式：</strong></td>
                                <td><code>JSON</code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-key me-2"></i>认证信息</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>认证方式：</strong></td>
                                <td><code>Bearer Token</code></td>
                            </tr>
                            <tr>
                                <td><strong>API密钥：</strong></td>
                                <td>
                                    <code id="apiKey">{{ api_key }}</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyApiKey()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>请求头：</strong></td>
                                <td><code>Authorization: Bearer {api_key}</code></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- API端点 -->
                <h5><i class="fas fa-list me-2"></i>API端点</h5>
                
                <!-- 执行编程任务 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <span class="badge bg-primary me-2">POST</span>
                            执行编程任务
                        </h6>
                    </div>
                    <div class="card-body">
                        <p><strong>端点：</strong> <code>/api/v1/programming-task</code></p>
                        <p><strong>描述：</strong> 提交编程任务，获取AI生成的代码解决方案</p>
                        
                        <h6>请求参数：</h6>
                        <pre><code class="language-json">{
  "task": "实现一个快速排序算法",
  "config_name": "default",
  "show_console": false
}</code></pre>

                        <h6>参数说明：</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>参数</th>
                                    <th>类型</th>
                                    <th>必填</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>task</code></td>
                                    <td>string</td>
                                    <td>是</td>
                                    <td>编程任务描述，1-5000字符</td>
                                </tr>
                                <tr>
                                    <td><code>config_name</code></td>
                                    <td>string</td>
                                    <td>否</td>
                                    <td>配置名称，默认"default"</td>
                                </tr>
                                <tr>
                                    <td><code>show_console</code></td>
                                    <td>boolean</td>
                                    <td>否</td>
                                    <td>是否显示控制台输出，默认false</td>
                                </tr>
                            </tbody>
                        </table>

                        <h6>响应示例：</h6>
                        <pre><code class="language-json">{
  "success": true,
  "task_id": "uuid-string",
  "result": {
    "messages": [
      {
        "source": "coder",
        "content": "def quick_sort(arr):\n    ...",
        "type": "text"
      }
    ],
    "stop_reason": "OPTIMIZATION_COMPLETE",
    "total_messages": 3
  },
  "execution_time": 15.6,
  "message_count": 3,
  "stop_reason": "OPTIMIZATION_COMPLETE"
}</code></pre>
                    </div>
                </div>

                <!-- 获取配置列表 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <span class="badge bg-success me-2">GET</span>
                            获取配置列表
                        </h6>
                    </div>
                    <div class="card-body">
                        <p><strong>端点：</strong> <code>/api/v1/configs</code></p>
                        <p><strong>描述：</strong> 获取所有可用的配置选项</p>
                        
                        <h6>响应示例：</h6>
                        <pre><code class="language-json">{
  "configs": [
    {
      "name": "default",
      "model_type": "deepseek-chat",
      "mode": "standard",
      "max_messages": 15,
      "description": "deepseek-chat - standard模式"
    }
  ]
}</code></pre>
                    </div>
                </div>

                <!-- 健康检查 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <span class="badge bg-info me-2">GET</span>
                            健康检查
                        </h6>
                    </div>
                    <div class="card-body">
                        <p><strong>端点：</strong> <code>/health</code></p>
                        <p><strong>描述：</strong> 检查API服务状态（无需认证）</p>
                        
                        <h6>响应示例：</h6>
                        <pre><code class="language-json">{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00"
}</code></pre>
                    </div>
                </div>

                <!-- 代码示例 -->
                <h5><i class="fas fa-code me-2"></i>代码示例</h5>
                
                <ul class="nav nav-tabs" id="codeExampleTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button">Python</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="javascript-tab" data-bs-toggle="tab" data-bs-target="#javascript" type="button">JavaScript</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button">cURL</button>
                    </li>
                </ul>
                
                <div class="tab-content" id="codeExampleTabsContent">
                    <div class="tab-pane fade show active" id="python" role="tabpanel">
                        <pre><code class="language-python">import requests
import json

# API配置
API_BASE_URL = "http://{{ api_domain }}:{{ api_port }}"
API_KEY = "{{ api_key }}"

def call_autogen_api(task, config_name="default"):
    """调用AutoGen API"""
    url = f"{API_BASE_URL}/api/v1/programming-task"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "task": task,
        "config_name": config_name,
        "show_console": False
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API调用失败: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    task = "实现一个Python函数来计算斐波那契数列"
    result = call_autogen_api(task)
    
    if result and result["success"]:
        print("任务执行成功！")
        for message in result["result"]["messages"]:
            print(f"{message['source']}: {message['content'][:100]}...")
    else:
        print("任务执行失败")</code></pre>
                    </div>
                    
                    <div class="tab-pane fade" id="javascript" role="tabpanel">
                        <pre><code class="language-javascript">// API配置
const API_BASE_URL = "http://{{ api_domain }}:{{ api_port }}";
const API_KEY = "{{ api_key }}";

async function callAutoGenAPI(task, configName = "default") {
    const url = `${API_BASE_URL}/api/v1/programming-task`;
    
    const headers = {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json"
    };
    
    const data = {
        task: task,
        config_name: configName,
        show_console: false
    };
    
    try {
        const response = await fetch(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error("API调用失败:", error);
        return null;
    }
}

// 使用示例
(async () => {
    const task = "实现一个JavaScript函数来排序数组";
    const result = await callAutoGenAPI(task);
    
    if (result && result.success) {
        console.log("任务执行成功！");
        result.result.messages.forEach(message => {
            console.log(`${message.source}: ${message.content.substring(0, 100)}...`);
        });
    } else {
        console.log("任务执行失败");
    }
})();</code></pre>
                    </div>
                    
                    <div class="tab-pane fade" id="curl" role="tabpanel">
                        <pre><code class="language-bash"># 执行编程任务
curl -X POST "http://{{ api_domain }}:{{ api_port }}/api/v1/programming-task" \
  -H "Authorization: Bearer {{ api_key }}" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "实现一个快速排序算法",
    "config_name": "default",
    "show_console": false
  }'

# 获取配置列表
curl -X GET "http://{{ api_domain }}:{{ api_port }}/api/v1/configs" \
  -H "Authorization: Bearer {{ api_key }}"

# 健康检查
curl -X GET "http://{{ api_domain }}:{{ api_port }}/health"</code></pre>
                    </div>
                </div>

                <!-- 错误代码 -->
                <h5 class="mt-4"><i class="fas fa-exclamation-triangle me-2"></i>错误代码</h5>
                <table class="table">
                    <thead>
                        <tr>
                            <th>状态码</th>
                            <th>错误类型</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>200</code></td>
                            <td>成功</td>
                            <td>请求处理成功</td>
                        </tr>
                        <tr>
                            <td><code>400</code></td>
                            <td>请求错误</td>
                            <td>请求参数不正确</td>
                        </tr>
                        <tr>
                            <td><code>401</code></td>
                            <td>认证失败</td>
                            <td>API密钥无效或缺失</td>
                        </tr>
                        <tr>
                            <td><code>500</code></td>
                            <td>服务器错误</td>
                            <td>服务器内部错误</td>
                        </tr>
                    </tbody>
                </table>

                <!-- 在线测试 -->
                <h5><i class="fas fa-play me-2"></i>在线测试</h5>
                <div class="card">
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="testTask" class="form-label">测试任务：</label>
                            <textarea class="form-control" id="testTask" rows="3" 
                                      placeholder="输入要测试的编程任务...">实现一个Python函数来计算两个数的最大公约数</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="testConfig" class="form-label">配置：</label>
                            <select class="form-select" id="testConfig">
                                <option value="default">default</option>
                                <option value="fast">fast</option>
                                <option value="thorough">thorough</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" id="testApiButton">
                            <i class="fas fa-play me-1"></i>测试API
                        </button>
                        <div id="testResult" class="mt-3" style="display: none;">
                            <h6>测试结果：</h6>
                            <pre><code id="testResultContent"></code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function copyApiKey() {
        const apiKey = document.getElementById('apiKey').textContent;
        navigator.clipboard.writeText(apiKey).then(() => {
            showAlert('API密钥已复制到剪贴板', 'success');
        });
    }
    
    // 在线测试功能
    document.getElementById('testApiButton').addEventListener('click', async function() {
        const task = document.getElementById('testTask').value.trim();
        const config = document.getElementById('testConfig').value;
        const resultDiv = document.getElementById('testResult');
        const resultContent = document.getElementById('testResultContent');
        
        if (!task) {
            showAlert('请输入测试任务', 'warning');
            return;
        }
        
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>测试中...';
        
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task: task,
                    config: config
                })
            });
            
            const data = await response.json();
            resultContent.textContent = JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
            
            if (data.success) {
                showAlert('API测试成功', 'success');
            } else {
                showAlert('API测试失败: ' + data.error, 'danger');
            }
        } catch (error) {
            resultContent.textContent = `错误: ${error.message}`;
            resultDiv.style.display = 'block';
            showAlert('测试请求失败', 'danger');
        } finally {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-play me-1"></i>测试API';
        }
    });
</script>
{% endblock %}
