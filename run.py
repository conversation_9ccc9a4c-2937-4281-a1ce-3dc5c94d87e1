#!/usr/bin/env python3
"""
AutoGen编程工作流启动脚本
提供简单的命令行界面来运行工作流
"""

import asyncio
import argparse
import sys
import os
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from autogen_programming_workflow import ProgrammingWorkflow
from config import list_available_configs, ModelType, WorkflowMode


def check_api_key() -> bool:
    """检查API密钥是否设置"""
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")

    if not deepseek_key and not openai_key:
        print("❌ 错误: 未设置API密钥")
        print("\n请设置API密钥:")
        print("  DeepSeek (推荐): set DEEPSEEK_API_KEY=your-deepseek-key")
        print("  OpenAI: set OPENAI_API_KEY=your-openai-key")
        print("  或创建.env文件: echo 'DEEPSEEK_API_KEY=your-key' > .env")
        return False

    if deepseek_key:
        print(f"✅ 使用DeepSeek API密钥: {deepseek_key[:20]}...")
    elif openai_key:
        print(f"✅ 使用OpenAI API密钥: {openai_key[:20]}...")

    return True


async def run_interactive_mode():
    """交互模式"""
    print("🤖 AutoGen编程工作流 - 交互模式")
    print("=" * 50)
    
    # 选择配置
    configs = list_available_configs()
    print("📋 可用配置:")
    for i, config in enumerate(configs, 1):
        print(f"  {i}. {config}")
    
    while True:
        try:
            choice = input(f"\n选择配置 (1-{len(configs)}) [默认: 1]: ").strip()
            if not choice:
                config_name = configs[0]
                break
            elif choice.isdigit() and 1 <= int(choice) <= len(configs):
                config_name = configs[int(choice) - 1]
                break
            else:
                print("❌ 无效选择，请重试")
        except KeyboardInterrupt:
            print("\n👋 再见!")
            return
    
    print(f"✅ 选择配置: {config_name}")
    
    # 创建工作流
    try:
        workflow = ProgrammingWorkflow(config_name=config_name)
        print(f"🚀 工作流已初始化 (模型: {workflow.config.model_type.value})")
    except Exception as e:
        print(f"❌ 工作流初始化失败: {e}")
        return
    
    # 主循环
    try:
        while True:
            print("\n" + "=" * 50)
            print("💡 请输入编程任务 (输入 'quit' 退出, 'help' 查看帮助):")
            
            task = input("> ").strip()
            
            if task.lower() in ['quit', 'exit', 'q']:
                break
            elif task.lower() in ['help', 'h']:
                print_help()
                continue
            elif not task:
                print("❌ 请输入有效的任务描述")
                continue
            
            print(f"\n🎯 执行任务: {task}")
            print("-" * 30)
            
            try:
                result = await workflow.run_programming_workflow(task)
                print(f"\n✅ 任务完成!")
                print(f"📊 生成消息数: {len(result.messages)}")
                print(f"🛑 停止原因: {result.stop_reason}")
                
                # 询问是否继续
                continue_choice = input("\n继续下一个任务? (y/n) [y]: ").strip().lower()
                if continue_choice in ['n', 'no']:
                    break
                    
            except KeyboardInterrupt:
                print("\n⏸️  任务被用户中断")
                continue_choice = input("继续使用工作流? (y/n) [y]: ").strip().lower()
                if continue_choice in ['n', 'no']:
                    break
            except Exception as e:
                print(f"❌ 任务执行失败: {e}")
                continue_choice = input("继续使用工作流? (y/n) [y]: ").strip().lower()
                if continue_choice in ['n', 'no']:
                    break
    
    finally:
        await workflow.close()
        print("\n👋 感谢使用AutoGen编程工作流!")


async def run_single_task(task: str, config_name: str = "default", 
                         show_console: bool = True) -> bool:
    """运行单个任务"""
    try:
        workflow = ProgrammingWorkflow(config_name=config_name)
        print(f"🚀 使用配置 '{config_name}' 执行任务...")
        
        result = await workflow.run_programming_workflow(task, show_console=show_console)
        
        print(f"\n✅ 任务完成!")
        print(f"📊 生成消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        await workflow.close()
        return True
        
    except Exception as e:
        print(f"❌ 任务执行失败: {e}")
        return False


def print_help():
    """打印帮助信息"""
    help_text = """
🆘 AutoGen编程工作流帮助

📝 任务示例:
  • "实现一个快速排序算法"
  • "创建一个简单的计算器类"
  • "编写一个文件读写工具"
  • "实现一个LRU缓存"

💡 使用技巧:
  • 描述要尽可能具体和详细
  • 可以指定编程语言、框架或库
  • 可以要求特定的功能或性能要求
  • 可以要求包含测试用例或使用示例

⚙️ 配置说明:
  • default: 标准配置，平衡质量和速度
  • fast: 快速配置，优先速度
  • thorough: 深度配置，优先质量
  • economic: 经济配置，使用更便宜的模型

🔧 命令:
  • help/h: 显示此帮助
  • quit/exit/q: 退出程序
    """
    print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AutoGen编程工作流启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run.py                                    # 交互模式
  python run.py -t "实现快速排序算法"                # 单任务模式
  python run.py -t "创建计算器" -c fast              # 指定配置
  python run.py -t "实现LRU缓存" -c thorough --quiet # 静默模式
        """
    )
    
    parser.add_argument(
        "-t", "--task",
        type=str,
        help="要执行的编程任务"
    )
    
    parser.add_argument(
        "-c", "--config",
        type=str,
        default="default",
        choices=list_available_configs(),
        help="使用的配置名称 (默认: default)"
    )
    
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="静默模式，不显示详细过程"
    )
    
    parser.add_argument(
        "--list-configs",
        action="store_true",
        help="列出所有可用配置"
    )
    
    args = parser.parse_args()
    
    # 列出配置
    if args.list_configs:
        print("📋 可用配置:")
        for config_name in list_available_configs():
            print(f"  • {config_name}")
        return
    
    # 检查API密钥
    if not check_api_key():
        sys.exit(1)
    
    try:
        if args.task:
            # 单任务模式
            print(f"🎯 单任务模式: {args.task}")
            success = asyncio.run(
                run_single_task(
                    args.task, 
                    args.config, 
                    show_console=not args.quiet
                )
            )
            sys.exit(0 if success else 1)
        else:
            # 交互模式
            asyncio.run(run_interactive_mode())
            
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
