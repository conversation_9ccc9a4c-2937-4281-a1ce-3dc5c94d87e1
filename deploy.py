#!/usr/bin/env python3
"""
AutoGen编程工作流部署脚本
用于部署API服务和Web服务到服务器
"""

import asyncio
import subprocess
import sys
import os
import signal
import time
from pathlib import Path
from typing import Optional, List
import argparse


class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes: List[subprocess.Popen] = []
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在关闭服务...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def check_dependencies(self) -> bool:
        """检查依赖是否安装"""
        print("🔍 检查依赖...")
        
        required_packages = [
            "fastapi",
            "uvicorn",
            "jinja2",
            "openai"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package}")
        
        if missing_packages:
            print(f"\n缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install fastapi uvicorn jinja2 openai python-multipart")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def install_dependencies(self):
        """安装依赖"""
        print("📦 安装依赖...")
        
        try:
            # 安装基础依赖
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--user",
                "fastapi", "uvicorn[standard]", "jinja2", "python-multipart"
            ], check=True)
            
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    def setup_environment(self):
        """设置环境"""
        print("🔧 设置环境...")
        
        # 确保模板目录存在
        templates_dir = self.project_root / "templates"
        if not templates_dir.exists():
            print(f"❌ 模板目录不存在: {templates_dir}")
            return False
        
        # 检查必要文件
        required_files = [
            "api_server.py",
            "web_server.py",
            "config.py",
            "autogen_programming_workflow.py"
        ]
        
        for file_name in required_files:
            file_path = self.project_root / file_name
            if not file_path.exists():
                print(f"❌ 缺少文件: {file_name}")
                return False
            print(f"✅ {file_name}")
        
        print("✅ 环境检查完成")
        return True
    
    def start_api_server(self, host: str = "0.0.0.0", port: int = 8000) -> Optional[subprocess.Popen]:
        """启动API服务"""
        print(f"🚀 启动API服务 ({host}:{port})...")
        
        try:
            process = subprocess.Popen([
                sys.executable, "-m", "uvicorn",
                "api_server:app",
                "--host", host,
                "--port", str(port),
                "--reload"
            ], cwd=self.project_root)
            
            self.processes.append(process)
            print(f"✅ API服务已启动 (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ API服务启动失败: {e}")
            return None
    
    def start_web_server(self, host: str = "0.0.0.0", port: int = 8080) -> Optional[subprocess.Popen]:
        """启动Web服务"""
        print(f"🌐 启动Web服务 ({host}:{port})...")
        
        try:
            process = subprocess.Popen([
                sys.executable, "-m", "uvicorn",
                "web_server:app",
                "--host", host,
                "--port", str(port),
                "--reload"
            ], cwd=self.project_root)
            
            self.processes.append(process)
            print(f"✅ Web服务已启动 (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ Web服务启动失败: {e}")
            return None
    
    def check_service_health(self, host: str, port: int, timeout: int = 30) -> bool:
        """检查服务健康状态"""
        import urllib.request
        import urllib.error
        
        url = f"http://{host}:{port}/health"
        
        for i in range(timeout):
            try:
                with urllib.request.urlopen(url, timeout=5) as response:
                    if response.status == 200:
                        return True
            except (urllib.error.URLError, urllib.error.HTTPError):
                pass
            
            time.sleep(1)
            if i % 5 == 0:
                print(f"⏳ 等待服务启动... ({i}/{timeout})")
        
        return False
    
    def stop_all_services(self):
        """停止所有服务"""
        print("🛑 停止所有服务...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=10)
                print(f"✅ 进程 {process.pid} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 强制终止进程 {process.pid}")
            except Exception as e:
                print(f"❌ 停止进程 {process.pid} 失败: {e}")
        
        self.processes.clear()
    
    def deploy_all(self, api_host: str = "0.0.0.0", api_port: int = 8000,
                   web_host: str = "0.0.0.0", web_port: int = 8080):
        """部署所有服务"""
        print("🚀 开始部署AutoGen编程工作流服务")
        print("=" * 60)
        
        # 检查依赖
        if not self.check_dependencies():
            if not self.install_dependencies():
                return False
        
        # 设置环境
        if not self.setup_environment():
            return False
        
        # 启动API服务
        api_process = self.start_api_server(api_host, api_port)
        if not api_process:
            return False
        
        # 等待API服务启动
        print("⏳ 等待API服务启动...")
        if not self.check_service_health(api_host, api_port):
            print("❌ API服务启动超时")
            self.stop_all_services()
            return False
        
        # 启动Web服务
        web_process = self.start_web_server(web_host, web_port)
        if not web_process:
            self.stop_all_services()
            return False
        
        # 等待Web服务启动
        print("⏳ 等待Web服务启动...")
        time.sleep(5)  # Web服务没有health端点，等待5秒
        
        print("\n" + "=" * 60)
        print("🎉 部署完成！")
        print(f"📡 API服务: http://{api_host}:{api_port}")
        print(f"📚 API文档: http://{api_host}:{api_port}/docs")
        print(f"🌐 Web界面: http://{web_host}:{web_port}")
        print("=" * 60)
        
        # 保持运行
        try:
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                for process in self.processes[:]:
                    if process.poll() is not None:
                        print(f"⚠️ 进程 {process.pid} 意外退出")
                        self.processes.remove(process)
                
                if not self.processes:
                    print("❌ 所有服务都已停止")
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 收到中断信号，正在关闭服务...")
        finally:
            self.stop_all_services()
        
        return True
    
    def deploy_api_only(self, host: str = "0.0.0.0", port: int = 8000):
        """仅部署API服务"""
        print("🚀 部署API服务")
        
        if not self.check_dependencies() or not self.setup_environment():
            return False
        
        api_process = self.start_api_server(host, port)
        if not api_process:
            return False
        
        print(f"📡 API服务: http://{host}:{port}")
        print(f"📚 API文档: http://{host}:{port}/docs")
        
        try:
            api_process.wait()
        except KeyboardInterrupt:
            print("\n👋 正在关闭API服务...")
            self.stop_all_services()
        
        return True
    
    def deploy_web_only(self, host: str = "0.0.0.0", port: int = 8080):
        """仅部署Web服务"""
        print("🌐 部署Web服务")
        
        if not self.check_dependencies() or not self.setup_environment():
            return False
        
        web_process = self.start_web_server(host, port)
        if not web_process:
            return False
        
        print(f"🌐 Web界面: http://{host}:{port}")
        
        try:
            web_process.wait()
        except KeyboardInterrupt:
            print("\n👋 正在关闭Web服务...")
            self.stop_all_services()
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoGen编程工作流部署脚本")
    parser.add_argument("--mode", choices=["all", "api", "web"], default="all",
                       help="部署模式 (default: all)")
    parser.add_argument("--api-host", default="0.0.0.0", help="API服务主机")
    parser.add_argument("--api-port", type=int, default=8000, help="API服务端口")
    parser.add_argument("--web-host", default="0.0.0.0", help="Web服务主机")
    parser.add_argument("--web-port", type=int, default=8080, help="Web服务端口")
    parser.add_argument("--install-deps", action="store_true", help="强制安装依赖")
    
    args = parser.parse_args()
    
    manager = DeploymentManager()
    
    # 强制安装依赖
    if args.install_deps:
        manager.install_dependencies()
    
    # 根据模式部署
    if args.mode == "all":
        success = manager.deploy_all(args.api_host, args.api_port, 
                                   args.web_host, args.web_port)
    elif args.mode == "api":
        success = manager.deploy_api_only(args.api_host, args.api_port)
    elif args.mode == "web":
        success = manager.deploy_web_only(args.web_host, args.web_port)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
