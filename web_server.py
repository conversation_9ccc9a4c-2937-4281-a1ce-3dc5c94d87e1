"""
AutoGen编程工作流Web服务
提供Web界面供用户直接使用
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from fastapi import FastAPI, Request, Form, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer
import uvicorn
from starlette.middleware.sessions import SessionMiddleware

from autogen_programming_workflow import ProgrammingWorkflow
from config import get_web_config, get_config, list_available_configs


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取配置
web_config = get_web_config()

# 创建FastAPI应用
app = FastAPI(
    title="AutoGen编程工作流Web界面",
    description="基于DeepSeek的多Agent编程协作Web服务",
    version="1.0.0"
)

# 添加会话中间件
app.add_middleware(
    SessionMiddleware,
    secret_key=web_config.secret_key,
    max_age=web_config.session_timeout
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=web_config.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模板和静态文件
templates = Jinja2Templates(directory="templates")

# 全局变量
active_sessions: Dict[str, Dict[str, Any]] = {}


def check_login(request: Request) -> bool:
    """检查用户是否已登录"""
    return request.session.get("logged_in", False)


def require_login(request: Request):
    """要求用户登录"""
    if not check_login(request):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录"
        )


@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路径，重定向到登录页"""
    if check_login(request):
        return RedirectResponse(url="/dashboard", status_code=302)
    return RedirectResponse(url="/login", status_code=302)


@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """登录页面"""
    if check_login(request):
        return RedirectResponse(url="/dashboard", status_code=302)
    
    return templates.TemplateResponse("login.html", {
        "request": request,
        "title": "AutoGen工作流 - 登录"
    })


@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...)
):
    """处理登录"""
    if username == web_config.username and password == web_config.password:
        request.session["logged_in"] = True
        request.session["username"] = username
        request.session["login_time"] = datetime.now().isoformat()
        
        logger.info(f"用户 {username} 登录成功")
        return RedirectResponse(url="/dashboard", status_code=302)
    else:
        logger.warning(f"用户 {username} 登录失败")
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "AutoGen工作流 - 登录",
            "error": "用户名或密码错误"
        })


@app.get("/logout")
async def logout(request: Request):
    """退出登录"""
    username = request.session.get("username", "unknown")
    request.session.clear()
    logger.info(f"用户 {username} 退出登录")
    return RedirectResponse(url="/login", status_code=302)


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """引导页/仪表板"""
    require_login(request)
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "AutoGen工作流 - 控制台",
        "username": request.session.get("username"),
        "api_domain": web_config.api_domain,
        "api_port": web_config.api_port
    })


@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """对话页面"""
    require_login(request)
    
    # 获取可用配置
    configs = []
    for config_name in list_available_configs():
        try:
            config = get_config(config_name)
            configs.append({
                "name": config_name,
                "display_name": f"{config_name} ({config.model_type.value})",
                "model_type": config.model_type.value,
                "mode": config.mode.value
            })
        except Exception as e:
            logger.error(f"获取配置 {config_name} 失败: {e}")
    
    return templates.TemplateResponse("chat.html", {
        "request": request,
        "title": "AutoGen工作流 - 对话",
        "username": request.session.get("username"),
        "configs": configs
    })


@app.get("/api-docs", response_class=HTMLResponse)
async def api_docs_page(request: Request):
    """API接入说明页面"""
    require_login(request)
    
    return templates.TemplateResponse("api_docs.html", {
        "request": request,
        "title": "AutoGen工作流 - API接入",
        "username": request.session.get("username"),
        "api_domain": web_config.api_domain,
        "api_port": web_config.api_port,
        "api_key": web_config.api_key
    })


@app.post("/api/chat")
async def chat_api(request: Request):
    """聊天API接口（非流式，保持兼容性）"""
    require_login(request)

    try:
        data = await request.json()
        task = data.get("task", "").strip()
        config_name = data.get("config", "default")

        if not task:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "任务描述不能为空"}
            )

        # 创建工作流
        workflow = ProgrammingWorkflow(config_name=config_name)

        # 执行任务
        result = await workflow.run_programming_workflow(task, show_console=False)

        # 处理结果
        messages = []
        for msg in result.messages:
            if hasattr(msg, 'source') and hasattr(msg, 'content'):
                messages.append({
                    "source": msg.source,
                    "content": msg.content,
                    "type": getattr(msg, 'type', 'text')
                })

        await workflow.close()

        return JSONResponse(content={
            "success": True,
            "messages": messages,
            "stop_reason": result.stop_reason,
            "message_count": len(result.messages)
        })

    except Exception as e:
        logger.error(f"聊天API错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"处理失败: {str(e)}"}
        )


@app.post("/api/chat/stream")
async def chat_stream_api(request: Request):
    """流式聊天API接口"""
    require_login(request)

    # 首先获取请求数据
    data = await request.json()
    task = data.get("task", "").strip()
    config_name = data.get("config", "default")

    if not task:
        return StreamingResponse(
            iter([f"data: {json.dumps({'type': 'error', 'message': '任务描述不能为空'})}\n\n"]),
            media_type="text/event-stream"
        )

    async def generate_stream():
        try:
            # 发送开始信号
            yield f"data: {json.dumps({'type': 'start', 'message': '🚀 开始编程工作流', 'config': config_name}, ensure_ascii=False)}\n\n"

            # 创建工作流
            workflow = ProgrammingWorkflow(config_name=config_name)

            # 发送工作流信息
            yield f"data: {json.dumps({'type': 'info', 'message': f'🤖 模型: {workflow.config.model_type.value}'}, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'info', 'message': f'📝 任务: {task}'}, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'info', 'message': '=' * 60}, ensure_ascii=False)}\n\n"

            # 发送处理中信号
            yield f"data: {json.dumps({'type': 'info', 'message': '⏳ 正在执行工作流...'}, ensure_ascii=False)}\n\n"

            # 执行任务
            result = await workflow.run_programming_workflow(task, show_console=False)

            # 逐个发送消息
            for i, msg in enumerate(result.messages):
                if hasattr(msg, 'source') and hasattr(msg, 'content'):
                    message_data = {
                        'type': 'message',
                        'index': i + 1,
                        'source': msg.source,
                        'content': msg.content,
                        'message_type': getattr(msg, 'type', 'text')
                    }
                    yield f"data: {json.dumps(message_data, ensure_ascii=False)}\n\n"

                    # 添加小延迟以模拟实时效果
                    await asyncio.sleep(0.3)

            # 发送完成信号
            completion_data = {
                'type': 'complete',
                'stop_reason': result.stop_reason,
                'message_count': len(result.messages),
                'message': '✅ 工作流完成!'
            }
            yield f"data: {json.dumps(completion_data, ensure_ascii=False)}\n\n"

            await workflow.close()

        except Exception as e:
            logger.error(f"流式聊天API错误: {str(e)}")
            error_data = {
                'type': 'error',
                'message': f'处理失败: {str(e)}'
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@app.get("/api/configs")
async def get_configs_api(request: Request):
    """获取配置API"""
    require_login(request)
    
    configs = []
    for config_name in list_available_configs():
        try:
            config = get_config(config_name)
            configs.append({
                "name": config_name,
                "model_type": config.model_type.value,
                "mode": config.mode.value,
                "max_messages": config.max_messages
            })
        except Exception as e:
            logger.error(f"获取配置 {config_name} 失败: {e}")
    
    return {"configs": configs}


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    if exc.status_code == 401:
        return RedirectResponse(url="/login", status_code=302)
    
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )


def run_web_server():
    """运行Web服务器"""
    logger.info(f"🌐 启动AutoGen Web服务")
    logger.info(f"🔗 Web域名: {web_config.web_domain}")
    logger.info(f"👤 登录用户名: {web_config.username}")
    logger.info(f"🔑 登录密码: {web_config.password}")
    logger.info(f"📱 Web界面: http://{web_config.web_domain}:{web_config.web_port}")
    
    uvicorn.run(
        "web_server:app",
        host="0.0.0.0",
        port=web_config.web_port,
        reload=web_config.debug,
        log_level="info"
    )


if __name__ == "__main__":
    run_web_server()
