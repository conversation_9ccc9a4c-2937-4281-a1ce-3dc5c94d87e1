# AutoGen编程工作流部署指南

## 🚀 部署概述

本指南将帮助您将AutoGen编程工作流部署为Web服务，支持API调用和Web界面两种访问方式。

### 服务架构

```
Internet
    ↓
您的Nginx配置 (反向代理)
    ↓
┌─────────────────┬─────────────────┐
│   API服务       │   Web服务       │
│ api.crawlonline │ chat.crawlonline│
│ :8000          │ :8080           │
└─────────────────┴─────────────────┘
```

### 域名配置

- **API域名**: `api.crawlonline.vip:8000`
- **Web域名**: `chat.crawlonline.vip:8080`

## 📋 部署要求

### 系统要求

- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **Python**: 3.10+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的互联网连接

### 软件依赖

- Python 3.10+
- pip
- Docker (可选)

## 🔧 快速部署

### 方法1: 直接部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd autogen-programming-workflow

# 2. 安装依赖
pip install --user fastapi uvicorn jinja2 python-multipart openai

# 3. 启动服务
python start_services.py
```

### 方法2: 使用部署脚本

```bash
# 部署所有服务
python deploy.py --mode all

# 仅部署API服务
python deploy.py --mode api --api-port 8000

# 仅部署Web服务
python deploy.py --mode web --web-port 8080
```

### 方法3: Docker部署

```bash
# 构建镜像
docker build -t autogen-workflow .

# 使用docker-compose部署
docker compose up -d

# 查看服务状态
docker compose ps
```

## ⚙️ 配置说明

### 核心配置 (config.py)

```python
@dataclass
class WebServiceConfig:
    # API服务配置
    api_domain: str = "api.crawlonline.vip"
    api_port: int = 8000
    api_key: str = "sk-autogen-api-2024-secure-key"
    
    # Web服务配置
    web_domain: str = "chat.crawlonline.vip"
    web_port: int = 8080
    
    # 登录配置
    username: str = "admin"
    password: str = "autogen2024"
```

### 环境变量

```bash
# DeepSeek API密钥
export DEEPSEEK_API_KEY="***********************************"

# 可选：OpenAI API密钥（备用）
export OPENAI_API_KEY="your-openai-key"
```

## 🌐 反向代理配置

### Nginx配置参考

由于您已有现有的Nginx配置，以下是推荐的代理配置供参考：

```nginx
# API服务代理配置
server {
    listen 80;
    server_name api.crawlonline.vip;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
}

# Web服务代理配置
server {
    listen 80;
    server_name chat.crawlonline.vip;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
}
```

**注意**: 请根据您现有的Nginx配置结构进行调整。

## 🔒 安全配置

### API密钥保护

- API密钥: `sk-autogen-api-2024-secure-key`
- 所有API请求必须包含 `Authorization: Bearer {api_key}` 头

### Web登录保护

- 用户名: `admin`
- 密码: `autogen2024`

### 防火墙设置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8000/tcp
sudo ufw allow 8080/tcp
sudo ufw enable
```

## 📊 服务监控

### 健康检查

```bash
# API服务健康检查
curl http://api.crawlonline.vip:8000/health

# 检查服务状态
curl -H "Authorization: Bearer sk-autogen-api-2024-secure-key" \
     http://api.crawlonline.vip:8000/
```

### 日志查看

```bash
# 查看API服务日志
tail -f /var/log/autogen-api.log

# 查看Web服务日志
tail -f /var/log/autogen-web.log

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :8000
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 使用国内镜像
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ fastapi uvicorn
   ```

3. **SSL证书问题**
   ```bash
   # 检查证书
   openssl x509 -in /etc/nginx/ssl/api.crawlonline.vip.crt -text -noout
   ```

4. **服务无法启动**
   ```bash
   # 检查Python路径
   which python3
   
   # 检查依赖
   python3 -c "import fastapi, uvicorn, jinja2, openai"
   ```

## 📈 性能优化

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化

- 使用多个worker进程
- 启用Gzip压缩
- 配置静态文件缓存
- 使用Redis缓存（可选）

## 🔄 更新部署

```bash
# 1. 停止服务
sudo systemctl stop autogen-api
sudo systemctl stop autogen-web

# 2. 更新代码
git pull origin main

# 3. 重启服务
python deploy.py --mode all
```

## 📞 技术支持

### 服务状态检查

- API文档: `https://api.crawlonline.vip/docs`
- Web界面: `https://chat.crawlonline.vip`
- 健康检查: `https://api.crawlonline.vip/health`

### 联系方式

如遇问题，请检查：
1. 服务日志
2. 网络连接
3. 配置文件
4. 依赖安装

---

**🎉 部署完成后，您就可以通过Web界面或API调用使用AutoGen编程工作流了！**
