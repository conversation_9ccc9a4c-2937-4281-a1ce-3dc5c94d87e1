"""
AutoGen编程工作流使用示例
展示不同类型的编程任务和工作流配置
"""

import asyncio
import os
from autogen_programming_workflow import ProgrammingWorkflow


class WorkflowExamples:
    """工作流示例集合"""
    
    def __init__(self):
        self.workflow = None
    
    async def setup_workflow(self, model_name: str = "gpt-4o"):
        """设置工作流"""
        # 确保设置了API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("⚠️  请设置OPENAI_API_KEY环境变量")
            return False
        
        self.workflow = ProgrammingWorkflow(model_name=model_name)
        return True
    
    async def example_1_data_structure(self):
        """示例1：数据结构实现"""
        print("\n" + "="*80)
        print("📚 示例1：数据结构实现 - 二叉搜索树")
        print("="*80)
        
        task = """
        请实现一个二叉搜索树(Binary Search Tree)类。
        
        要求：
        1. 支持插入(insert)、搜索(search)、删除(delete)操作
        2. 实现中序遍历(inorder_traversal)方法
        3. 包含树的高度计算(height)方法
        4. 添加完整的错误处理和边界条件检查
        5. 包含详细的文档字符串和类型注解
        6. 实现__str__方法用于可视化树结构
        
        请确保代码具有良好的性能和可读性。
        """
        
        result = await self.workflow.run_programming_workflow(task)
        return result
    
    async def example_2_algorithm(self):
        """示例2：算法实现"""
        print("\n" + "="*80)
        print("🧮 示例2：算法实现 - 动态规划")
        print("="*80)
        
        await self.workflow.reset_workflow()
        
        task = """
        请实现一个动态规划解决方案来解决"最长公共子序列"(LCS)问题。
        
        要求：
        1. 实现递归版本和动态规划版本
        2. 包含时间复杂度和空间复杂度分析
        3. 添加测试用例和性能比较
        4. 支持字符串和列表两种输入类型
        5. 包含详细的算法解释和注释
        6. 实现结果回溯，返回实际的最长公共子序列
        
        请优化空间复杂度，并提供清晰的算法说明。
        """
        
        result = await self.workflow.run_programming_workflow(task)
        return result
    
    async def example_3_web_scraper(self):
        """示例3：实用工具开发"""
        print("\n" + "="*80)
        print("🌐 示例3：实用工具 - 网页爬虫")
        print("="*80)
        
        await self.workflow.reset_workflow()
        
        task = """
        请实现一个简单但功能完整的网页爬虫类。
        
        要求：
        1. 支持GET和POST请求
        2. 包含请求头设置和用户代理轮换
        3. 实现请求频率限制和重试机制
        4. 支持HTML解析和数据提取
        5. 包含异常处理和日志记录
        6. 支持保存数据到JSON或CSV文件
        7. 遵循robots.txt协议
        8. 包含使用示例和文档
        
        请确保代码是异步的，具有良好的性能和错误处理。
        """
        
        result = await self.workflow.run_programming_workflow(task)
        return result
    
    async def example_4_api_client(self):
        """示例4：API客户端开发"""
        print("\n" + "="*80)
        print("🔌 示例4：API客户端 - RESTful API封装")
        print("="*80)
        
        await self.workflow.reset_workflow()
        
        task = """
        请实现一个通用的RESTful API客户端类。
        
        要求：
        1. 支持GET、POST、PUT、DELETE等HTTP方法
        2. 包含认证机制(API Key, Bearer Token, Basic Auth)
        3. 实现请求和响应的序列化/反序列化
        4. 包含错误处理和重试逻辑
        5. 支持请求缓存和速率限制
        6. 实现异步请求处理
        7. 包含日志记录和调试功能
        8. 提供简洁的链式调用API
        
        请设计一个易于使用且可扩展的接口。
        """
        
        result = await self.workflow.run_programming_workflow(task)
        return result
    
    async def example_5_design_pattern(self):
        """示例5：设计模式实现"""
        print("\n" + "="*80)
        print("🏗️ 示例5：设计模式 - 观察者模式")
        print("="*80)
        
        await self.workflow.reset_workflow()
        
        task = """
        请实现一个完整的观察者模式(Observer Pattern)。
        
        要求：
        1. 实现Subject(主题)和Observer(观察者)接口
        2. 支持多种类型的事件和数据传递
        3. 包含异步观察者支持
        4. 实现观察者优先级和过滤机制
        5. 包含错误处理，防止单个观察者错误影响其他观察者
        6. 支持观察者的动态添加和移除
        7. 实现弱引用，避免内存泄漏
        8. 包含完整的使用示例和测试
        
        请确保实现是线程安全的，并具有良好的性能。
        """
        
        result = await self.workflow.run_programming_workflow(task)
        return result
    
    async def run_all_examples(self):
        """运行所有示例"""
        if not await self.setup_workflow():
            return
        
        try:
            examples = [
                ("数据结构实现", self.example_1_data_structure),
                ("算法实现", self.example_2_algorithm),
                ("实用工具开发", self.example_3_web_scraper),
                ("API客户端开发", self.example_4_api_client),
                ("设计模式实现", self.example_5_design_pattern),
            ]
            
            results = []
            for name, example_func in examples:
                print(f"\n🎯 开始执行: {name}")
                try:
                    result = await example_func()
                    results.append((name, result, "成功"))
                    print(f"✅ {name} 完成")
                except Exception as e:
                    print(f"❌ {name} 失败: {str(e)}")
                    results.append((name, None, f"失败: {str(e)}"))
            
            # 打印总结
            print("\n" + "="*80)
            print("📊 执行总结")
            print("="*80)
            for name, result, status in results:
                print(f"{name}: {status}")
            
        finally:
            await self.workflow.close()
    
    async def run_single_example(self, example_name: str):
        """运行单个示例"""
        if not await self.setup_workflow():
            return
        
        examples = {
            "data_structure": self.example_1_data_structure,
            "algorithm": self.example_2_algorithm,
            "web_scraper": self.example_3_web_scraper,
            "api_client": self.example_4_api_client,
            "design_pattern": self.example_5_design_pattern,
        }
        
        if example_name not in examples:
            print(f"❌ 未找到示例: {example_name}")
            print(f"可用示例: {list(examples.keys())}")
            return
        
        try:
            result = await examples[example_name]()
            print(f"✅ 示例 {example_name} 执行完成")
            return result
        except Exception as e:
            print(f"❌ 示例 {example_name} 执行失败: {str(e)}")
        finally:
            await self.workflow.close()


async def main():
    """主函数"""
    examples = WorkflowExamples()
    
    # 可以选择运行所有示例或单个示例
    import sys
    
    if len(sys.argv) > 1:
        example_name = sys.argv[1]
        await examples.run_single_example(example_name)
    else:
        print("🚀 AutoGen编程工作流示例")
        print("选择运行模式:")
        print("1. 运行所有示例")
        print("2. 运行单个示例")
        
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            await examples.run_all_examples()
        elif choice == "2":
            print("\n可用示例:")
            print("- data_structure: 数据结构实现")
            print("- algorithm: 算法实现")
            print("- web_scraper: 网页爬虫")
            print("- api_client: API客户端")
            print("- design_pattern: 设计模式")
            
            example_name = input("\n请输入示例名称: ").strip()
            await examples.run_single_example(example_name)
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    asyncio.run(main())
