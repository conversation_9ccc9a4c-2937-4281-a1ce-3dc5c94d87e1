"""
DeepSeek API测试文件
测试DeepSeek模型的连接和功能
"""

import asyncio
import os
import sys
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from autogen_programming_workflow import ProgrammingWorkflow
from config import WorkflowConfig, ModelType, WorkflowMode


async def test_deepseek_connection(api_key: str) -> bool:
    """测试DeepSeek API连接"""
    print("🔌 测试DeepSeek API连接...")
    
    try:
        # 创建简单的配置
        config = WorkflowConfig(
            model_type=ModelType.DEEPSEEK_CHAT,
            base_url="https://api.deepseek.com",
            api_key=api_key,
            max_messages=3,  # 限制消息数以节省成本
            show_console=False
        )
        
        workflow = ProgrammingWorkflow(config=config)
        
        # 简单的测试任务
        test_task = "请写一个简单的Python函数来计算两个数的和。"
        
        print(f"📝 测试任务: {test_task}")
        result = await workflow.run_programming_workflow(test_task, show_console=False)
        
        print("✅ DeepSeek API连接成功!")
        print(f"📊 生成消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        # 显示第一个agent的响应
        for msg in result.messages:
            if hasattr(msg, 'source') and msg.source == 'coder':
                print(f"🤖 Coder响应预览: {msg.content[:200]}...")
                break
        
        await workflow.close()
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False


async def test_deepseek_models(api_key: str):
    """测试不同的DeepSeek模型"""
    print("\n🧪 测试不同DeepSeek模型...")
    
    models = [
        (ModelType.DEEPSEEK_CHAT, "DeepSeek Chat (V3)"),
        (ModelType.DEEPSEEK_REASONER, "DeepSeek Reasoner (R1)")
    ]
    
    test_task = "实现一个简单的计算器函数，支持加减乘除。"
    
    for model_type, model_name in models:
        print(f"\n🔬 测试模型: {model_name}")
        print("-" * 40)
        
        try:
            config = WorkflowConfig(
                model_type=model_type,
                base_url="https://api.deepseek.com",
                api_key=api_key,
                max_messages=5,
                show_console=False
            )
            
            workflow = ProgrammingWorkflow(config=config)
            result = await workflow.run_programming_workflow(test_task, show_console=False)
            
            print(f"✅ {model_name} 测试成功")
            print(f"📊 消息数: {len(result.messages)}")
            
            await workflow.close()
            
        except Exception as e:
            print(f"❌ {model_name} 测试失败: {e}")


async def test_deepseek_configurations(api_key: str):
    """测试不同的DeepSeek配置"""
    print("\n⚙️  测试不同配置...")
    
    configs = ["default", "fast", "thorough", "economic"]
    test_task = "写一个函数来检查字符串是否为回文。"
    
    for config_name in configs:
        print(f"\n🔧 测试配置: {config_name}")
        print("-" * 30)
        
        try:
            # 设置环境变量
            os.environ["DEEPSEEK_API_KEY"] = api_key
            
            workflow = ProgrammingWorkflow(config_name=config_name)
            result = await workflow.run_programming_workflow(test_task, show_console=False)
            
            print(f"✅ 配置 {config_name} 测试成功")
            print(f"📊 消息数: {len(result.messages)}")
            print(f"🤖 模型: {workflow.config.model_type.value}")
            
            await workflow.close()
            
        except Exception as e:
            print(f"❌ 配置 {config_name} 测试失败: {e}")


async def benchmark_deepseek_performance(api_key: str):
    """DeepSeek性能基准测试"""
    print("\n⏱️  DeepSeek性能基准测试...")
    
    import time
    
    test_task = "实现一个简单的栈数据结构，包含push、pop、peek操作。"
    
    try:
        config = WorkflowConfig(
            model_type=ModelType.DEEPSEEK_CHAT,
            base_url="https://api.deepseek.com",
            api_key=api_key,
            max_messages=10,
            show_console=False
        )
        
        workflow = ProgrammingWorkflow(config=config)
        
        start_time = time.time()
        result = await workflow.run_programming_workflow(test_task, show_console=False)
        end_time = time.time()
        
        duration = end_time - start_time
        message_count = len(result.messages)
        
        print(f"⏱️  执行时间: {duration:.2f} 秒")
        print(f"📊 消息数量: {message_count}")
        print(f"🚀 平均每消息时间: {duration/message_count:.2f} 秒")
        
        # 计算token使用情况（如果可用）
        total_prompt_tokens = 0
        total_completion_tokens = 0
        
        for msg in result.messages:
            if hasattr(msg, 'models_usage') and msg.models_usage:
                if hasattr(msg.models_usage, 'prompt_tokens'):
                    total_prompt_tokens += msg.models_usage.prompt_tokens
                if hasattr(msg.models_usage, 'completion_tokens'):
                    total_completion_tokens += msg.models_usage.completion_tokens
        
        if total_prompt_tokens > 0 or total_completion_tokens > 0:
            print(f"🎯 Prompt Tokens: {total_prompt_tokens}")
            print(f"📝 Completion Tokens: {total_completion_tokens}")
            print(f"💰 总Token数: {total_prompt_tokens + total_completion_tokens}")
        
        await workflow.close()
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")


async def run_deepseek_demo(api_key: str):
    """运行DeepSeek演示"""
    print("\n🎯 DeepSeek工作流演示...")
    
    demo_task = """
    请实现一个Python类来管理学生信息。
    要求：
    1. 包含学生姓名、年龄、成绩等属性
    2. 支持添加、删除、查找学生
    3. 支持计算平均成绩
    4. 包含适当的错误处理
    5. 添加文档字符串和类型注解
    """
    
    try:
        # 使用thorough配置进行演示
        os.environ["DEEPSEEK_API_KEY"] = api_key
        workflow = ProgrammingWorkflow(config_name="thorough")
        
        print(f"📝 演示任务: {demo_task[:100]}...")
        print("🚀 开始执行工作流...")
        
        result = await workflow.run_programming_workflow(demo_task, show_console=True)
        
        print("\n" + "="*60)
        print("🎉 演示完成!")
        print(f"📊 总消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        await workflow.close()
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 DeepSeek API测试套件")
    print("=" * 50)
    
    # 使用提供的API密钥
    api_key = "sk-747b3a99f3b340e8a2a48592e2f6aaf1"
    
    # 设置环境变量
    os.environ["DEEPSEEK_API_KEY"] = api_key
    
    print(f"🔑 使用API密钥: {api_key[:20]}...")
    
    # 运行测试
    tests = [
        ("连接测试", lambda: test_deepseek_connection(api_key)),
        ("模型测试", lambda: test_deepseek_models(api_key)),
        ("配置测试", lambda: test_deepseek_configurations(api_key)),
        ("性能测试", lambda: benchmark_deepseek_performance(api_key)),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            await test_func()
            results.append((test_name, "✅ 通过"))
        except Exception as e:
            results.append((test_name, f"❌ 失败: {str(e)}"))
            print(f"❌ {test_name} 失败: {str(e)}")
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    for test_name, status in results:
        print(f"{test_name}: {status}")
    
    # 统计
    passed = sum(1 for _, status in results if "✅" in status)
    total = len(results)
    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    # 如果基础测试通过，询问是否运行演示
    if passed > 0:
        try:
            choice = input("\n🎯 是否运行完整演示? (y/n) [n]: ").strip().lower()
            if choice in ['y', 'yes']:
                await run_deepseek_demo(api_key)
        except KeyboardInterrupt:
            print("\n👋 测试被用户中断")


if __name__ == "__main__":
    asyncio.run(main())
