"""
简单的DeepSeek API测试
直接使用OpenAI客户端测试DeepSeek API连接
"""

import asyncio
import sys

try:
    from openai import OpenAI
    print("✅ OpenAI库已安装")
except ImportError:
    print("❌ 请先安装OpenAI库: pip install openai")
    sys.exit(1)


def test_deepseek_sync():
    """同步测试DeepSeek API"""
    print("🔌 测试DeepSeek API连接 (同步)...")
    
    api_key = "***********************************"
    
    try:
        # 创建DeepSeek客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        # 测试简单的聊天完成
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "你是一个专业的Python程序员。"},
                {"role": "user", "content": "请写一个简单的Python函数来计算两个数的和。"}
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        print("✅ DeepSeek API连接成功!")
        print(f"🤖 模型: {response.model}")
        print(f"📝 响应: {response.choices[0].message.content[:200]}...")
        print(f"🎯 使用Token: {response.usage.total_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False


async def test_deepseek_async():
    """异步测试DeepSeek API"""
    print("\n🔌 测试DeepSeek API连接 (异步)...")
    
    api_key = "***********************************"
    
    try:
        from openai import AsyncOpenAI
        
        # 创建异步DeepSeek客户端
        client = AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        # 测试异步聊天完成
        response = await client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "你是一个代码审查专家。"},
                {"role": "user", "content": "请审查这个函数: def add(a, b): return a + b"}
            ],
            max_tokens=300,
            temperature=0.5
        )
        
        print("✅ DeepSeek异步API连接成功!")
        print(f"🤖 模型: {response.model}")
        print(f"📝 响应: {response.choices[0].message.content[:200]}...")
        print(f"🎯 使用Token: {response.usage.total_tokens}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek异步API连接失败: {e}")
        return False


def test_deepseek_reasoner():
    """测试DeepSeek Reasoner模型"""
    print("\n🧠 测试DeepSeek Reasoner模型...")
    
    api_key = "***********************************"
    
    try:
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        # 测试推理模型
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=[
                {"role": "user", "content": "请分析并优化这个排序算法的时间复杂度: def bubble_sort(arr): n = len(arr); for i in range(n): for j in range(0, n-i-1): if arr[j] > arr[j+1]: arr[j], arr[j+1] = arr[j+1], arr[j]; return arr"}
            ],
            max_tokens=800,
            temperature=0.3
        )
        
        print("✅ DeepSeek Reasoner模型连接成功!")
        print(f"🤖 模型: {response.model}")
        print(f"📝 响应: {response.choices[0].message.content[:300]}...")
        print(f"🎯 使用Token: {response.usage.total_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek Reasoner模型连接失败: {e}")
        return False


def test_deepseek_streaming():
    """测试DeepSeek流式响应"""
    print("\n🌊 测试DeepSeek流式响应...")
    
    api_key = "***********************************"
    
    try:
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        # 测试流式响应
        stream = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "请写一个Python类来实现简单的栈数据结构。"}
            ],
            max_tokens=400,
            temperature=0.6,
            stream=True
        )
        
        print("✅ DeepSeek流式响应开始...")
        print("📝 实时响应:")
        
        full_response = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_response += content
        
        print(f"\n\n✅ 流式响应完成! 总长度: {len(full_response)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek流式响应失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 DeepSeek API简单测试")
    print("=" * 50)
    
    tests = [
        ("同步API测试", test_deepseek_sync),
        ("Reasoner模型测试", test_deepseek_reasoner),
        ("流式响应测试", test_deepseek_streaming),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            success = test_func()
            results.append((test_name, "✅ 通过" if success else "❌ 失败"))
        except Exception as e:
            results.append((test_name, f"❌ 异常: {str(e)}"))
            print(f"❌ {test_name} 发生异常: {str(e)}")
    
    # 异步测试
    print(f"\n🧪 运行: 异步API测试")
    try:
        success = asyncio.run(test_deepseek_async())
        results.append(("异步API测试", "✅ 通过" if success else "❌ 失败"))
    except Exception as e:
        results.append(("异步API测试", f"❌ 异常: {str(e)}"))
        print(f"❌ 异步API测试发生异常: {str(e)}")
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    for test_name, status in results:
        print(f"{test_name}: {status}")
    
    # 统计
    passed = sum(1 for _, status in results if "✅" in status)
    total = len(results)
    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed > 0:
        print("\n🎉 DeepSeek API配置成功!")
        print("💡 您现在可以使用DeepSeek模型运行AutoGen工作流了。")
    else:
        print("\n❌ DeepSeek API配置失败，请检查API密钥和网络连接。")


if __name__ == "__main__":
    main()
