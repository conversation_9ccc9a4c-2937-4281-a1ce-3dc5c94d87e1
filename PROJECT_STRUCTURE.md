# 项目结构说明

## 📁 文件组织

```
autogen-programming-workflow/
├── 📄 README.md                      # 项目主要文档
├── 📄 INSTALL.md                     # 安装指南
├── 📄 PROJECT_STRUCTURE.md           # 项目结构说明（本文件）
├── 📄 requirements.txt               # Python依赖列表
├── 🐍 autogen_programming_workflow.py # 核心工作流实现
├── 🐍 config.py                      # 配置管理模块
├── 🐍 demo.py                        # 演示脚本
├── 🐍 workflow_examples.py           # 详细使用示例
└── 🐍 test_workflow.py               # 测试文件
```

## 📋 文件详细说明

### 核心文件

#### `autogen_programming_workflow.py`
- **作用**: 主要的工作流实现
- **包含**: 
  - `ProgrammingWorkflow` 类
  - 三个Agent的设置和管理
  - 工作流执行逻辑
- **关键类**:
  - `ProgrammingWorkflow`: 主工作流类

#### `config.py`
- **作用**: 配置管理和系统消息模板
- **包含**:
  - 模型类型枚举
  - 工作模式枚举
  - 配置数据类
  - 系统消息模板
  - 配置管理器
- **关键类**:
  - `ModelType`: 支持的模型类型
  - `WorkflowMode`: 工作流模式
  - `WorkflowConfig`: 工作流配置
  - `ConfigManager`: 配置管理器
  - `SystemMessages`: 系统消息模板

### 示例和演示文件

#### `demo.py`
- **作用**: 交互式演示脚本
- **包含**:
  - 基本用法演示
  - 不同模式比较
  - 自定义配置示例
  - 复杂任务演示
  - 性能比较
- **使用**: `python demo.py`

#### `workflow_examples.py`
- **作用**: 详细的使用示例集合
- **包含**:
  - 数据结构实现示例
  - 算法实现示例
  - 实用工具开发示例
  - API客户端开发示例
  - 设计模式实现示例
- **使用**: `python workflow_examples.py [example_name]`

### 测试文件

#### `test_workflow.py`
- **作用**: 工作流测试和验证
- **包含**:
  - 单元测试
  - 集成测试
  - 性能基准测试
  - 模拟测试
- **使用**: `python test_workflow.py`

### 配置文件

#### `requirements.txt`
- **作用**: Python依赖包列表
- **包含**: 所有必需和可选的依赖包
- **使用**: `pip install -r requirements.txt`

### 文档文件

#### `README.md`
- **作用**: 项目主要文档
- **包含**: 项目介绍、特性、快速开始、API文档

#### `INSTALL.md`
- **作用**: 详细安装指南
- **包含**: 系统要求、安装步骤、配置说明、故障排除

## 🔄 工作流程

### 1. 初始化流程
```
用户创建ProgrammingWorkflow实例
    ↓
加载配置（config.py）
    ↓
创建模型客户端
    ↓
设置三个Agent（使用SystemMessages）
    ↓
配置终止条件
    ↓
创建RoundRobinGroupChat团队
```

### 2. 执行流程
```
用户提供编程任务
    ↓
Agent1(Coder) 编写初始代码
    ↓
Agent2(Reviewer) 审查代码并提出建议
    ↓
Agent3(Optimizer) 根据建议优化代码
    ↓
检查终止条件
    ↓
如果未满足，继续下一轮迭代
    ↓
返回TaskResult
```

## 🎯 使用场景

### 快速开始
```python
# 最简单的使用方式
from autogen_programming_workflow import ProgrammingWorkflow

workflow = ProgrammingWorkflow()
result = await workflow.run_programming_workflow("实现一个排序函数")
```

### 自定义配置
```python
# 使用预设配置
workflow = ProgrammingWorkflow(config_name="fast")

# 或使用自定义配置
from config import WorkflowConfig, ModelType
config = WorkflowConfig(model_type=ModelType.GPT_4O_MINI)
workflow = ProgrammingWorkflow(config=config)
```

### 运行示例
```bash
# 运行演示
python demo.py

# 运行特定示例
python workflow_examples.py data_structure

# 运行测试
python test_workflow.py
```

## 🔧 扩展指南

### 添加新的Agent
1. 在 `autogen_programming_workflow.py` 中创建新的Agent
2. 在 `config.py` 中添加对应的系统消息
3. 更新团队配置

### 添加新的配置模式
1. 在 `config.py` 中的 `WorkflowMode` 枚举添加新模式
2. 在 `SystemMessages` 中添加对应的消息模板
3. 在 `ConfigManager` 中添加预设配置

### 添加新的示例
1. 在 `workflow_examples.py` 中添加新的示例方法
2. 更新 `run_all_examples` 和 `run_single_example` 方法
3. 在文档中添加说明

## 📊 性能考虑

### 模型选择
- `gpt-4o`: 最佳质量，较高成本
- `gpt-4o-mini`: 平衡选择
- `gpt-3.5-turbo`: 最经济

### 模式选择
- `fast`: 快速完成，适合简单任务
- `standard`: 平衡质量和速度
- `thorough`: 深度分析，适合复杂任务

### 优化建议
- 对于简单任务使用 `fast` 模式
- 设置合理的 `max_messages` 限制
- 使用 `show_console=False` 减少输出开销

## 🔒 安全考虑

### API密钥管理
- 使用环境变量存储API密钥
- 不要在代码中硬编码密钥
- 使用 `.env` 文件（但不要提交到版本控制）

### 代码执行安全
- 工作流生成的代码需要人工审查后再执行
- 不要自动执行未验证的代码
- 在沙箱环境中测试生成的代码

## 🤝 贡献指南

### 代码风格
- 遵循PEP 8规范
- 使用类型注解
- 添加详细的文档字符串

### 测试要求
- 为新功能添加测试
- 确保所有测试通过
- 添加性能基准测试

### 文档更新
- 更新相关的README和文档
- 添加使用示例
- 更新项目结构说明

## 📞 支持

如需帮助：
1. 查看文档和示例
2. 运行测试诊断问题
3. 查看AutoGen官方文档
4. 创建Issue报告问题
