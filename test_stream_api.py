#!/usr/bin/env python3
"""
测试流式API的脚本
验证新的流式响应功能
"""

import requests
import json
import time


def test_stream_api():
    """测试流式API"""
    
    base_url = "http://localhost:8080"
    session = requests.Session()
    
    print("🌊 测试流式 AutoGen API")
    print("=" * 50)
    
    try:
        # 1. 登录
        print("1️⃣ 登录...")
        login_data = {"username": "admin", "password": "autogen2024"}
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        # 2. 测试流式API
        print("\n2️⃣ 测试流式响应...")
        task_data = {
            "task": "编写一个Python函数，实现快速排序算法",
            "config": "fast"
        }
        
        print(f"📝 任务: {task_data['task']}")
        print("⏳ 开始流式处理...")
        print("-" * 50)
        
        start_time = time.time()
        
        # 发送流式请求
        response = session.post(
            f"{base_url}/api/chat/stream",
            json=task_data,
            headers={"Content-Type": "application/json"},
            stream=True
        )
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        message_count = 0
        
        # 处理流式响应
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])  # 移除 'data: ' 前缀
                    
                    if data['type'] == 'start':
                        print(f"🚀 {data['message']} ({data['config']})")
                    elif data['type'] == 'info':
                        print(f"ℹ️  {data['message']}")
                    elif data['type'] == 'message':
                        message_count += 1
                        agent_name = data['source'].upper()
                        content_preview = data['content'][:100] + "..." if len(data['content']) > 100 else data['content']
                        print(f"\n🤖 {agent_name} (消息 {data['index']}):")
                        print(f"   {content_preview}")
                    elif data['type'] == 'complete':
                        end_time = time.time()
                        print(f"\n✅ {data['message']}")
                        print(f"📊 总消息数: {data['message_count']}")
                        print(f"🛑 停止原因: {data['stop_reason']}")
                        print(f"⏱️  总耗时: {end_time - start_time:.2f} 秒")
                        break
                    elif data['type'] == 'error':
                        print(f"\n❌ 错误: {data['message']}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON解析错误: {e}")
                    print(f"   原始数据: {line}")
                except KeyError as e:
                    print(f"⚠️  数据格式错误: {e}")
                    print(f"   数据内容: {data}")
        
        print("-" * 50)
        print("🎉 流式API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complex_task():
    """测试复杂任务的流式响应"""
    
    base_url = "http://localhost:8080"
    session = requests.Session()
    
    print("\n" + "=" * 50)
    print("🌟 测试复杂任务流式响应")
    print("=" * 50)
    
    try:
        # 登录
        login_data = {"username": "admin", "password": "autogen2024"}
        session.post(f"{base_url}/login", data=login_data)
        
        # 复杂任务
        task_data = {
            "task": "使用three.js实现基于纯html的太阳系运行动画。要求包含太阳和八大行星，具有真实的运行轨道和自转效果，支持动画控制和视角切换。",
            "config": "default"
        }
        
        print(f"📝 任务: {task_data['task'][:50]}...")
        print("⏳ 开始复杂任务流式处理...")
        print("-" * 50)
        
        start_time = time.time()
        
        response = session.post(
            f"{base_url}/api/chat/stream",
            json=task_data,
            headers={"Content-Type": "application/json"},
            stream=True,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return False
        
        message_count = 0
        total_content_length = 0
        
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    
                    if data['type'] == 'start':
                        print(f"🚀 {data['message']} ({data['config']})")
                    elif data['type'] == 'info':
                        print(f"ℹ️  {data['message']}")
                    elif data['type'] == 'message':
                        message_count += 1
                        agent_name = data['source'].upper()
                        content_length = len(data['content'])
                        total_content_length += content_length
                        
                        print(f"\n🤖 {agent_name} (消息 {data['index']}):")
                        print(f"   📏 内容长度: {content_length:,} 字符")
                        
                        # 显示内容预览
                        if 'html' in data['content'].lower() or '```' in data['content']:
                            print(f"   📄 包含代码块")
                        else:
                            preview = data['content'][:150] + "..." if len(data['content']) > 150 else data['content']
                            print(f"   📝 内容预览: {preview}")
                            
                    elif data['type'] == 'complete':
                        end_time = time.time()
                        print(f"\n✅ {data['message']}")
                        print(f"📊 总消息数: {data['message_count']}")
                        print(f"📏 总内容长度: {total_content_length:,} 字符")
                        print(f"🛑 停止原因: {data['stop_reason']}")
                        print(f"⏱️  总耗时: {end_time - start_time:.2f} 秒")
                        print(f"📈 平均速度: {total_content_length / (end_time - start_time):.0f} 字符/秒")
                        break
                    elif data['type'] == 'error':
                        print(f"\n❌ 错误: {data['message']}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON解析错误: {e}")
                except KeyError as e:
                    print(f"⚠️  数据格式错误: {e}")
        
        print("-" * 50)
        print("🎉 复杂任务流式测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 复杂任务测试中出现异常: {e}")
        return False


def main():
    """主函数"""
    print("🧪 AutoGen 流式 API 测试程序")
    print("=" * 50)
    
    # 测试基本流式功能
    basic_success = test_stream_api()
    
    if basic_success:
        print("\n🎉 基本流式功能测试通过！")
        
        # 询问是否测试复杂任务
        try:
            user_input = input("\n是否测试复杂任务流式响应？(y/N): ").strip().lower()
            if user_input in ['y', 'yes']:
                complex_success = test_complex_task()
                if complex_success:
                    print("\n🌟 复杂任务流式测试也通过了！")
                else:
                    print("\n❌ 复杂任务流式测试失败")
            else:
                print("\n⏭️  跳过复杂任务测试")
        except KeyboardInterrupt:
            print("\n\n👋 测试被用户中断")
    else:
        print("\n❌ 基本流式功能测试失败，请检查服务器状态")


if __name__ == "__main__":
    main()
