#!/bin/bash

# AutoGen Docker部署修复脚本

echo "🔧 AutoGen Docker部署修复脚本"
echo "================================"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker &> /dev/null; then
    echo "❌ docker命令未找到"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 停止现有容器
echo "🛑 停止现有容器..."
docker compose down

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker rmi augment-work-autogen-api augment-work-autogen-web 2>/dev/null || true

# 重新构建镜像
echo "🔨 重新构建镜像..."
docker compose build --no-cache

if [ $? -ne 0 ]; then
    echo "❌ 镜像构建失败"
    exit 1
fi

# 启动服务
echo "🚀 启动服务..."
docker compose up -d

if [ $? -ne 0 ]; then
    echo "❌ 服务启动失败"
    exit 1
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查容器状态
echo "📊 检查容器状态..."
docker compose ps

# 检查日志
echo "📋 检查服务日志..."
echo "--- API服务日志 ---"
docker compose logs --tail=10 autogen-api

echo "--- Web服务日志 ---"
docker compose logs --tail=10 autogen-web

# 测试服务
echo "🧪 测试服务..."

# 测试API服务
echo "测试API服务..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常"
fi

# 测试Web服务
echo "测试Web服务..."
if curl -s http://localhost:8080/ > /dev/null; then
    echo "✅ Web服务正常"
else
    echo "❌ Web服务异常"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📱 访问地址："
echo "  API服务: http://localhost:8000"
echo "  API文档: http://localhost:8000/docs"
echo "  Web界面: http://localhost:8080"
echo ""
echo "🔑 登录信息："
echo "  用户名: admin"
echo "  密码: autogen2024"
echo ""
echo "📋 管理命令："
echo "  查看状态: docker compose ps"
echo "  查看日志: docker compose logs -f"
echo "  停止服务: docker compose down"
