"""
AutoGen编程工作流演示脚本
展示如何使用不同配置运行编程任务
"""

import asyncio
import os
from autogen_programming_workflow import ProgrammingWorkflow
from config import WorkflowConfig, ModelType, WorkflowMode, list_available_configs


async def demo_basic_usage():
    """演示基本用法"""
    print("🎯 演示1: 基本用法")
    print("=" * 50)
    
    # 使用默认配置
    workflow = ProgrammingWorkflow()
    
    task = """
    请实现一个Python函数来计算斐波那契数列的第n项。
    要求：
    1. 支持递归和迭代两种实现
    2. 包含性能比较
    3. 添加完整的错误处理
    4. 包含使用示例
    """
    
    try:
        result = await workflow.run_programming_workflow(task)
        print(f"✅ 任务完成，共生成 {len(result.messages)} 条消息")
    except Exception as e:
        print(f"❌ 任务失败: {e}")
    finally:
        await workflow.close()


async def demo_different_modes():
    """演示不同模式"""
    print("\n🎯 演示2: 不同工作模式")
    print("=" * 50)
    
    task = """
    实现一个简单的LRU缓存类。
    要求：
    1. 支持get和put操作
    2. 固定容量，超出时淘汰最久未使用的项
    3. O(1)时间复杂度
    """
    
    modes = ["fast", "default", "thorough"]
    
    for mode in modes:
        print(f"\n🚀 运行模式: {mode}")
        print("-" * 30)
        
        try:
            workflow = ProgrammingWorkflow(config_name=mode)
            result = await workflow.run_programming_workflow(task, show_console=False)
            
            print(f"✅ {mode}模式完成")
            print(f"📊 消息数: {len(result.messages)}")
            print(f"🛑 停止原因: {result.stop_reason}")
            
            await workflow.close()
            
        except Exception as e:
            print(f"❌ {mode}模式失败: {e}")


async def demo_custom_config():
    """演示自定义配置"""
    print("\n🎯 演示3: 自定义配置")
    print("=" * 50)
    
    # 创建自定义配置
    custom_config = WorkflowConfig(
        model_type=ModelType.GPT_4O_MINI,  # 使用更经济的模型
        max_messages=12,                   # 自定义最大消息数
        mode=WorkflowMode.CUSTOM,          # 自定义模式
        show_console=False                 # 不显示控制台输出
    )
    
    workflow = ProgrammingWorkflow(config=custom_config)
    
    task = """
    实现一个简单的JSON配置文件读写工具。
    要求：
    1. 支持读取和写入JSON配置
    2. 支持嵌套配置访问（如config.get('database.host')）
    3. 包含配置验证功能
    """
    
    try:
        result = await workflow.run_programming_workflow(task)
        print(f"✅ 自定义配置任务完成")
        print(f"📊 消息数: {len(result.messages)}")
        
    except Exception as e:
        print(f"❌ 自定义配置任务失败: {e}")
    finally:
        await workflow.close()


async def demo_complex_task():
    """演示复杂任务"""
    print("\n🎯 演示4: 复杂编程任务")
    print("=" * 50)
    
    # 使用thorough模式处理复杂任务
    workflow = ProgrammingWorkflow(config_name="thorough")
    
    task = """
    设计并实现一个简单的任务调度器(Task Scheduler)。
    
    要求：
    1. 支持定时任务和延迟任务
    2. 支持任务优先级
    3. 支持任务取消和重试
    4. 线程安全
    5. 支持异步任务
    6. 包含任务状态监控
    7. 支持任务依赖关系
    8. 包含完整的错误处理
    9. 提供简洁的API接口
    10. 包含使用示例和测试
    
    这是一个复杂的系统设计任务，请仔细考虑架构设计。
    """
    
    try:
        result = await workflow.run_programming_workflow(task)
        print(f"✅ 复杂任务完成")
        print(f"📊 消息数: {len(result.messages)}")
        
        # 分析消息分布
        agent_stats = {}
        for msg in result.messages:
            if hasattr(msg, 'source') and msg.source != 'user':
                agent_stats[msg.source] = agent_stats.get(msg.source, 0) + 1
        
        print("📈 Agent消息分布:")
        for agent, count in agent_stats.items():
            print(f"  {agent}: {count} 条消息")
        
    except Exception as e:
        print(f"❌ 复杂任务失败: {e}")
    finally:
        await workflow.close()


async def demo_iterative_improvement():
    """演示迭代改进过程"""
    print("\n🎯 演示5: 迭代改进过程")
    print("=" * 50)
    
    workflow = ProgrammingWorkflow(config_name="default")
    
    # 第一个任务：基础实现
    task1 = """
    实现一个简单的计算器类，支持基本的四则运算。
    要求：
    1. 支持加减乘除运算
    2. 包含基本的错误处理
    """
    
    print("📝 第一轮：基础实现")
    try:
        result1 = await workflow.run_programming_workflow(task1, show_console=False)
        print(f"✅ 第一轮完成，消息数: {len(result1.messages)}")
    except Exception as e:
        print(f"❌ 第一轮失败: {e}")
        return
    
    # 第二个任务：功能扩展（基于之前的上下文）
    task2 = """
    基于之前的计算器实现，请扩展以下功能：
    1. 支持科学计算（sin, cos, log等）
    2. 支持表达式解析（如"2 + 3 * 4"）
    3. 支持变量存储和使用
    4. 添加计算历史记录
    5. 改进错误处理和用户体验
    """
    
    print("\n📝 第二轮：功能扩展")
    try:
        result2 = await workflow.run_programming_workflow(task2, show_console=False)
        print(f"✅ 第二轮完成，消息数: {len(result2.messages)}")
        
        # 显示总体统计
        total_messages = len(result1.messages) + len(result2.messages)
        print(f"📊 总计消息数: {total_messages}")
        
    except Exception as e:
        print(f"❌ 第二轮失败: {e}")
    finally:
        await workflow.close()


async def demo_performance_comparison():
    """演示性能比较"""
    print("\n🎯 演示6: 性能比较")
    print("=" * 50)
    
    import time
    
    task = "实现一个简单的排序算法比较工具，包含冒泡排序、快速排序和归并排序。"
    
    configs = ["fast", "default"]
    results = {}
    
    for config_name in configs:
        print(f"\n⏱️  测试配置: {config_name}")
        
        start_time = time.time()
        
        try:
            workflow = ProgrammingWorkflow(config_name=config_name)
            result = await workflow.run_programming_workflow(task, show_console=False)
            
            end_time = time.time()
            duration = end_time - start_time
            
            results[config_name] = {
                'duration': duration,
                'messages': len(result.messages),
                'success': True
            }
            
            print(f"✅ 完成时间: {duration:.2f}秒")
            print(f"📊 消息数: {len(result.messages)}")
            
            await workflow.close()
            
        except Exception as e:
            results[config_name] = {
                'duration': 0,
                'messages': 0,
                'success': False,
                'error': str(e)
            }
            print(f"❌ 失败: {e}")
    
    # 性能比较总结
    print("\n📈 性能比较总结:")
    print("-" * 30)
    for config_name, stats in results.items():
        if stats['success']:
            print(f"{config_name}: {stats['duration']:.2f}秒, {stats['messages']}条消息")
        else:
            print(f"{config_name}: 失败 - {stats.get('error', '未知错误')}")


async def main():
    """主演示函数"""
    print("🚀 AutoGen编程工作流演示")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置OPENAI_API_KEY环境变量")
        print("   export OPENAI_API_KEY='your-api-key'")
        return
    
    # 显示可用配置
    print("📋 可用配置:")
    for config_name in list_available_configs():
        print(f"  - {config_name}")
    
    # 运行演示
    demos = [
        ("基本用法", demo_basic_usage),
        ("不同模式", demo_different_modes),
        ("自定义配置", demo_custom_config),
        ("复杂任务", demo_complex_task),
        ("迭代改进", demo_iterative_improvement),
        ("性能比较", demo_performance_comparison),
    ]
    
    print("\n🎯 选择要运行的演示:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"  {i}. {name}")
    print("  0. 运行所有演示")
    
    try:
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == "0":
            # 运行所有演示
            for name, demo_func in demos:
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    await demo_func()
                except Exception as e:
                    print(f"❌ {name} 演示失败: {e}")
        elif choice.isdigit() and 1 <= int(choice) <= len(demos):
            # 运行选定的演示
            name, demo_func = demos[int(choice) - 1]
            print(f"\n{'='*20} {name} {'='*20}")
            await demo_func()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
