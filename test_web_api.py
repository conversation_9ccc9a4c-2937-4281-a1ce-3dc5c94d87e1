#!/usr/bin/env python3
"""
测试 Web API 的脚本
验证修复后的 AutoGen 工作流是否正常工作
"""

import requests
import json
import time


def test_web_api():
    """测试 Web API"""
    
    base_url = "http://localhost:8080"
    
    print("🌐 测试 AutoGen Web API")
    print("=" * 50)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 测试登录
        print("1️⃣ 测试登录...")
        login_data = {
            "username": "admin",
            "password": "autogen2024"
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        # 2. 测试获取配置
        print("\n2️⃣ 测试获取配置...")
        config_response = session.get(f"{base_url}/api/configs")
        if config_response.status_code == 200:
            configs = config_response.json()
            print(f"✅ 获取配置成功，共 {len(configs['configs'])} 个配置")
            for config in configs['configs']:
                print(f"   - {config['name']}: {config['model_type']} ({config['mode']})")
        else:
            print(f"❌ 获取配置失败: {config_response.status_code}")
            return False
        
        # 3. 测试简单任务
        print("\n3️⃣ 测试简单编程任务...")
        task_data = {
            "task": "编写一个Python函数，计算两个数的和",
            "config": "fast"
        }
        
        print(f"📝 任务: {task_data['task']}")
        print("⏳ 正在处理...")
        
        start_time = time.time()
        chat_response = session.post(
            f"{base_url}/api/chat",
            json=task_data,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"✅ 任务完成！")
            print(f"⏱️  处理时间: {end_time - start_time:.2f} 秒")
            print(f"📊 消息数: {result['message_count']}")
            print(f"🛑 停止原因: {result['stop_reason']}")
            
            # 显示部分结果
            if result['messages']:
                for msg in result['messages']:
                    print(f"\n🤖 {msg['source'].upper()}:")
                    content = msg['content']
                    if len(content) > 200:
                        content = content[:200] + "..."
                    print(f"   {content}")
            
            return True
        else:
            print(f"❌ 任务失败: {chat_response.status_code}")
            try:
                error_info = chat_response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {chat_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False


def test_solar_system_task():
    """测试太阳系动画任务"""
    
    base_url = "http://localhost:8080"
    session = requests.Session()
    
    print("\n" + "=" * 50)
    print("🌟 测试太阳系动画任务")
    print("=" * 50)
    
    try:
        # 登录
        login_data = {"username": "admin", "password": "autogen2024"}
        session.post(f"{base_url}/login", data=login_data)
        
        # 太阳系任务
        task_data = {
            "task": "使用three.js实现基于纯html的太阳系运行动画。要求包含太阳和八大行星，具有真实的运行轨道和自转效果，支持动画控制和视角切换。",
            "config": "default"
        }
        
        print(f"📝 任务: {task_data['task'][:50]}...")
        print("⏳ 正在处理（这可能需要几分钟）...")
        
        start_time = time.time()
        chat_response = session.post(
            f"{base_url}/api/chat",
            json=task_data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5分钟超时
        )
        end_time = time.time()
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"✅ 太阳系任务完成！")
            print(f"⏱️  处理时间: {end_time - start_time:.2f} 秒")
            print(f"📊 消息数: {result['message_count']}")
            print(f"🛑 停止原因: {result['stop_reason']}")
            
            # 保存结果
            with open("web_api_solar_system_result.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("💾 结果已保存到 web_api_solar_system_result.json")
            
            return True
        else:
            print(f"❌ 太阳系任务失败: {chat_response.status_code}")
            try:
                error_info = chat_response.json()
                print(f"   错误信息: {error_info}")
            except:
                print(f"   响应内容: {chat_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 太阳系任务测试中出现异常: {e}")
        return False


def main():
    """主函数"""
    print("🧪 AutoGen Web API 测试程序")
    print("=" * 50)
    
    # 测试基本功能
    basic_success = test_web_api()
    
    if basic_success:
        print("\n🎉 基本功能测试通过！")
        
        # 询问是否测试太阳系任务
        try:
            user_input = input("\n是否测试太阳系动画任务？(y/N): ").strip().lower()
            if user_input in ['y', 'yes']:
                solar_success = test_solar_system_task()
                if solar_success:
                    print("\n🌟 太阳系任务测试也通过了！")
                else:
                    print("\n❌ 太阳系任务测试失败")
            else:
                print("\n⏭️  跳过太阳系任务测试")
        except KeyboardInterrupt:
            print("\n\n👋 测试被用户中断")
    else:
        print("\n❌ 基本功能测试失败，请检查服务器状态")


if __name__ == "__main__":
    main()
