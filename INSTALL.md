# AutoGen编程工作流安装指南

## 📋 系统要求

- Python 3.10 或更高版本
- DeepSeek API 密钥（推荐）或 OpenAI API 密钥
- 至少 4GB 可用内存
- 稳定的网络连接

## 🚀 快速安装

### 1. 克隆或下载项目

```bash
# 如果是从Git仓库克隆
git clone <repository-url>
cd autogen-programming-workflow

# 或者直接下载文件到本地目录
```

### 2. 创建虚拟环境（推荐）

```bash
# 创建虚拟环境
python -m venv autogen_env

# 激活虚拟环境
# Windows:
autogen_env\Scripts\activate
# macOS/Linux:
source autogen_env/bin/activate
```

### 3. 安装依赖

```bash
# 安装所有依赖
pip install -r requirements.txt

# 或者手动安装核心依赖
pip install autogen-agentchat autogen-ext[openai] openai
```

### 4. 设置API密钥

#### 方法1: 环境变量（推荐）

```bash
# 设置DeepSeek API密钥（推荐，更经济）
# Windows (PowerShell)
$env:DEEPSEEK_API_KEY="***********************************"

# Windows (CMD)
set DEEPSEEK_API_KEY=***********************************

# macOS/Linux
export DEEPSEEK_API_KEY="***********************************"

# 或者设置OpenAI API密钥
# export OPENAI_API_KEY="your-openai-api-key"
```

#### 方法2: .env文件

```bash
# 创建.env文件
echo "DEEPSEEK_API_KEY=***********************************" > .env
```

#### 方法3: 代码中直接指定

```python
from config import WorkflowConfig, ModelType

config = WorkflowConfig(
    model_type=ModelType.DEEPSEEK_CHAT,
    base_url="https://api.deepseek.com",
    api_key="***********************************"
)
```

## 🧪 验证安装

### 运行测试

```bash
# 运行基本测试
python test_workflow.py

# 运行演示
python demo.py
```

### 快速验证

```python
import asyncio
from autogen_programming_workflow import ProgrammingWorkflow

async def test():
    workflow = ProgrammingWorkflow()
    task = "写一个Hello World函数"
    result = await workflow.run_programming_workflow(task, show_console=False)
    print(f"测试成功！生成了 {len(result.messages)} 条消息")
    await workflow.close()

asyncio.run(test())
```

## 📦 依赖说明

### 核心依赖

- `autogen-agentchat`: AutoGen的AgentChat框架
- `autogen-ext[openai]`: AutoGen扩展，包含OpenAI集成
- `autogen-core`: AutoGen核心库
- `openai`: OpenAI Python客户端

### 可选依赖

- `aiohttp`, `httpx`: 用于网页爬虫示例
- `beautifulsoup4`, `lxml`: 用于HTML解析
- `pandas`, `numpy`: 用于数据处理示例
- `pytest`, `pytest-asyncio`: 用于测试

## 🔧 配置选项

### 模型配置

支持的模型：
- `gpt-4o` (默认，最佳质量)
- `gpt-4o-mini` (经济选择)
- `gpt-4-turbo` (高性能)
- `gpt-3.5-turbo` (最经济)

### 工作模式

- `standard`: 标准模式，平衡质量和速度
- `fast`: 快速模式，优先速度
- `thorough`: 深度模式，优先质量
- `custom`: 自定义模式

### 预设配置

- `default`: 默认配置 (gpt-4o, standard模式)
- `fast`: 快速配置 (gpt-4o-mini, fast模式)
- `thorough`: 深度配置 (gpt-4o, thorough模式)
- `economic`: 经济配置 (gpt-3.5-turbo, fast模式)

## 🚨 常见问题

### 1. 安装失败

**问题**: `pip install` 失败
**解决方案**:
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 分步安装
pip install autogen-agentchat
pip install autogen-ext[openai]
```

### 2. API密钥错误

**问题**: `AuthenticationError` 或 `Invalid API key`
**解决方案**:
- 检查API密钥是否正确
- 确认API密钥有足够的额度
- 检查环境变量是否正确设置

### 3. 网络连接问题

**问题**: 连接超时或网络错误
**解决方案**:
```python
# 设置代理（如果需要）
import os
os.environ['HTTP_PROXY'] = 'http://your-proxy:port'
os.environ['HTTPS_PROXY'] = 'http://your-proxy:port'
```

### 4. 内存不足

**问题**: 运行时内存不足
**解决方案**:
- 使用更小的模型 (`gpt-4o-mini`)
- 减少最大消息数限制
- 使用 `show_console=False` 减少输出

### 5. Python版本不兼容

**问题**: Python版本过低
**解决方案**:
```bash
# 检查Python版本
python --version

# 升级Python到3.10+
# 或使用pyenv管理多个Python版本
```

## 🔄 更新

### 更新依赖

```bash
# 更新所有依赖到最新版本
pip install --upgrade -r requirements.txt

# 更新特定包
pip install --upgrade autogen-agentchat autogen-ext
```

### 检查更新

```bash
# 检查过时的包
pip list --outdated

# 显示包信息
pip show autogen-agentchat
```

## 🐳 Docker部署（可选）

### Dockerfile示例

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

ENV OPENAI_API_KEY=""

CMD ["python", "demo.py"]
```

### 构建和运行

```bash
# 构建镜像
docker build -t autogen-workflow .

# 运行容器
docker run -e OPENAI_API_KEY="your-key" autogen-workflow
```

## 📞 获取帮助

如果遇到问题：

1. 检查本文档的常见问题部分
2. 查看项目的README.md文件
3. 运行测试脚本诊断问题
4. 查看AutoGen官方文档
5. 创建Issue报告问题

## 🔗 相关链接

- [AutoGen官方文档](https://microsoft.github.io/autogen/)
- [OpenAI API文档](https://platform.openai.com/docs)
- [Python虚拟环境指南](https://docs.python.org/3/tutorial/venv.html)
