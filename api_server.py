"""
AutoGen编程工作流API服务
提供RESTful API接口供外部调用
"""

import asyncio
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Security, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from autogen_programming_workflow import ProgrammingWorkflow
from config import get_web_config, get_config, list_available_configs


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取配置
web_config = get_web_config()

# 安全认证
security = HTTPBearer()


class TaskRequest(BaseModel):
    """编程任务请求模型"""
    task: str = Field(..., description="编程任务描述", min_length=1, max_length=5000)
    config_name: str = Field("default", description="使用的配置名称")
    show_console: bool = Field(False, description="是否显示控制台输出")


class TaskResponse(BaseModel):
    """编程任务响应模型"""
    success: bool = Field(..., description="任务是否成功")
    task_id: str = Field(..., description="任务ID")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: float = Field(..., description="执行时间（秒）")
    message_count: int = Field(..., description="生成的消息数量")
    stop_reason: Optional[str] = Field(None, description="停止原因")


class ConfigInfo(BaseModel):
    """配置信息模型"""
    name: str = Field(..., description="配置名称")
    model_type: str = Field(..., description="模型类型")
    mode: str = Field(..., description="工作模式")
    max_messages: int = Field(..., description="最大消息数")


class APIStatus(BaseModel):
    """API状态模型"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    timestamp: str = Field(..., description="当前时间")
    available_configs: List[ConfigInfo] = Field(..., description="可用配置")


# 全局变量
active_workflows: Dict[str, ProgrammingWorkflow] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 AutoGen API服务启动中...")
    yield
    logger.info("🛑 AutoGen API服务关闭中...")
    
    # 清理活动的工作流
    for workflow in active_workflows.values():
        try:
            await workflow.close()
        except Exception as e:
            logger.error(f"关闭工作流时出错: {e}")
    active_workflows.clear()


# 创建FastAPI应用
app = FastAPI(
    title="AutoGen编程工作流API",
    description="基于DeepSeek的多Agent编程协作API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=web_config.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> str:
    """验证API密钥"""
    if credentials.credentials != web_config.api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return credentials.credentials


@app.get("/", response_model=APIStatus)
async def get_api_status():
    """获取API状态"""
    configs = []
    for config_name in list_available_configs():
        try:
            config = get_config(config_name)
            configs.append(ConfigInfo(
                name=config_name,
                model_type=config.model_type.value,
                mode=config.mode.value,
                max_messages=config.max_messages
            ))
        except Exception as e:
            logger.error(f"获取配置 {config_name} 失败: {e}")
    
    return APIStatus(
        status="running",
        version="1.0.0",
        timestamp=datetime.now().isoformat(),
        available_configs=configs
    )


@app.post("/api/v1/programming-task", response_model=TaskResponse)
async def execute_programming_task(
    request: TaskRequest,
    api_key: str = Depends(verify_api_key)
) -> TaskResponse:
    """执行编程任务"""
    import time
    import uuid
    
    task_id = str(uuid.uuid4())
    start_time = time.time()
    
    logger.info(f"开始执行任务 {task_id}: {request.task[:100]}...")
    
    try:
        # 创建工作流
        workflow = ProgrammingWorkflow(config_name=request.config_name)
        active_workflows[task_id] = workflow
        
        # 执行任务
        result = await workflow.run_programming_workflow(
            request.task,
            show_console=request.show_console
        )
        
        execution_time = time.time() - start_time
        
        # 处理结果
        messages_data = []
        for msg in result.messages:
            msg_data = {
                "source": getattr(msg, 'source', 'unknown'),
                "content": getattr(msg, 'content', ''),
                "type": getattr(msg, 'type', 'unknown')
            }
            if hasattr(msg, 'models_usage') and msg.models_usage:
                msg_data["models_usage"] = {
                    "prompt_tokens": getattr(msg.models_usage, 'prompt_tokens', 0),
                    "completion_tokens": getattr(msg.models_usage, 'completion_tokens', 0)
                }
            messages_data.append(msg_data)
        
        response_data = {
            "messages": messages_data,
            "stop_reason": result.stop_reason,
            "total_messages": len(result.messages)
        }
        
        logger.info(f"任务 {task_id} 执行成功，耗时 {execution_time:.2f}秒")
        
        return TaskResponse(
            success=True,
            task_id=task_id,
            result=response_data,
            execution_time=execution_time,
            message_count=len(result.messages),
            stop_reason=result.stop_reason
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"任务执行失败: {str(e)}"
        logger.error(f"任务 {task_id} 失败: {error_msg}")
        logger.error(traceback.format_exc())
        
        return TaskResponse(
            success=False,
            task_id=task_id,
            error=error_msg,
            execution_time=execution_time,
            message_count=0
        )
        
    finally:
        # 清理工作流
        if task_id in active_workflows:
            try:
                await active_workflows[task_id].close()
                del active_workflows[task_id]
            except Exception as e:
                logger.error(f"清理工作流 {task_id} 时出错: {e}")


@app.get("/api/v1/configs")
async def get_available_configs(api_key: str = Depends(verify_api_key)):
    """获取可用配置列表"""
    configs = []
    for config_name in list_available_configs():
        try:
            config = get_config(config_name)
            configs.append({
                "name": config_name,
                "model_type": config.model_type.value,
                "mode": config.mode.value,
                "max_messages": config.max_messages,
                "description": f"{config.model_type.value} - {config.mode.value}模式"
            })
        except Exception as e:
            logger.error(f"获取配置 {config_name} 失败: {e}")
    
    return {"configs": configs}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "服务器内部错误",
            "detail": str(exc) if web_config.debug else "请联系管理员"
        }
    )


def run_api_server():
    """运行API服务器"""
    logger.info(f"🚀 启动AutoGen API服务")
    logger.info(f"📡 API域名: {web_config.api_domain}")
    logger.info(f"🔑 API密钥: {web_config.api_key}")
    logger.info(f"📚 API文档: http://{web_config.api_domain}:{web_config.api_port}/docs")
    
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=web_config.api_port,
        reload=web_config.debug,
        log_level="info"
    )


if __name__ == "__main__":
    run_api_server()
