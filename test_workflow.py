"""
AutoGen编程工作流测试文件
用于验证工作流的基本功能
"""

import asyncio
import os
import sys
from unittest.mock import AsyncMock, MagicMock
import pytest

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from autogen_programming_workflow import ProgrammingWorkflow


class MockModelClient:
    """模拟的模型客户端，用于测试"""
    
    def __init__(self):
        self.call_count = 0
        self.responses = [
            "这是一个简单的Python函数实现：\n\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fi<PERSON><PERSON><PERSON>(n-2)",
            "代码审查：这个递归实现简单但效率不高，建议添加迭代版本和缓存优化。",
            "优化后的代码：\n\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    return b\n\nOPTIMIZATION_COMPLETE"
        ]
    
    async def create(self, messages, **kwargs):
        """模拟创建聊天完成"""
        response_content = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1
        
        # 模拟OpenAI响应格式
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = response_content
        mock_response.usage.prompt_tokens = 100
        mock_response.usage.completion_tokens = 50
        
        return mock_response
    
    async def close(self):
        """模拟关闭连接"""
        pass


class TestProgrammingWorkflow:
    """编程工作流测试类"""
    
    @pytest.fixture
    async def workflow(self):
        """创建测试用的工作流实例"""
        workflow = ProgrammingWorkflow()
        # 替换为模拟客户端
        workflow.model_client = MockModelClient()
        
        # 重新设置agents使用模拟客户端
        workflow._setup_agents()
        workflow._setup_team()
        
        yield workflow
        await workflow.close()
    
    @pytest.mark.asyncio
    async def test_workflow_initialization(self, workflow):
        """测试工作流初始化"""
        assert workflow.coder_agent is not None
        assert workflow.reviewer_agent is not None
        assert workflow.optimizer_agent is not None
        assert workflow.team is not None
        assert workflow.termination_condition is not None
    
    @pytest.mark.asyncio
    async def test_agent_names(self, workflow):
        """测试Agent名称设置"""
        assert workflow.coder_agent.name == "coder"
        assert workflow.reviewer_agent.name == "reviewer"
        assert workflow.optimizer_agent.name == "optimizer"
    
    @pytest.mark.asyncio
    async def test_workflow_reset(self, workflow):
        """测试工作流重置功能"""
        # 这个测试需要实际的重置功能
        await workflow.reset_workflow()
        # 验证重置后的状态
        assert True  # 简单验证，实际应该检查内部状态
    
    def test_system_messages(self, workflow):
        """测试系统消息设置"""
        assert "程序员" in workflow.coder_agent.system_message
        assert "审查" in workflow.reviewer_agent.system_message
        assert "优化" in workflow.optimizer_agent.system_message


async def test_simple_workflow():
    """简单的工作流测试"""
    print("🧪 开始简单工作流测试...")
    
    # 检查是否有API密钥（用于集成测试）
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  未设置OPENAI_API_KEY，跳过集成测试")
        return
    
    try:
        workflow = ProgrammingWorkflow()
        
        # 简单的测试任务
        task = """
        请实现一个简单的Python函数来计算两个数的最大公约数(GCD)。
        要求：
        1. 使用欧几里得算法
        2. 包含输入验证
        3. 添加文档字符串
        """
        
        print(f"📝 测试任务: {task[:50]}...")
        
        # 运行工作流（不显示控制台输出）
        result = await workflow.run_programming_workflow(task, show_console=False)
        
        # 验证结果
        assert result is not None
        assert len(result.messages) > 0
        assert result.stop_reason is not None
        
        print("✅ 简单工作流测试通过")
        print(f"📊 消息数量: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        raise
    finally:
        await workflow.close()


async def test_workflow_components():
    """测试工作流组件"""
    print("🔧 测试工作流组件...")
    
    try:
        workflow = ProgrammingWorkflow()
        
        # 测试组件存在性
        assert hasattr(workflow, 'coder_agent')
        assert hasattr(workflow, 'reviewer_agent')
        assert hasattr(workflow, 'optimizer_agent')
        assert hasattr(workflow, 'team')
        assert hasattr(workflow, 'termination_condition')
        
        # 测试Agent配置
        assert workflow.coder_agent.name == "coder"
        assert workflow.reviewer_agent.name == "reviewer"
        assert workflow.optimizer_agent.name == "optimizer"
        
        print("✅ 组件测试通过")
        
    except Exception as e:
        print(f"❌ 组件测试失败: {str(e)}")
        raise
    finally:
        await workflow.close()


async def benchmark_workflow():
    """工作流性能基准测试"""
    print("⏱️  开始性能基准测试...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  未设置OPENAI_API_KEY，跳过性能测试")
        return
    
    import time
    
    try:
        workflow = ProgrammingWorkflow()
        
        # 简单任务用于性能测试
        task = "请实现一个Python函数来反转字符串。"
        
        start_time = time.time()
        result = await workflow.run_programming_workflow(task, show_console=False)
        end_time = time.time()
        
        duration = end_time - start_time
        message_count = len(result.messages)
        
        print(f"⏱️  执行时间: {duration:.2f} 秒")
        print(f"📊 消息数量: {message_count}")
        print(f"🚀 平均每消息时间: {duration/message_count:.2f} 秒")
        
        # 计算token使用情况
        total_prompt_tokens = sum(
            msg.models_usage.prompt_tokens 
            for msg in result.messages 
            if msg.models_usage and hasattr(msg.models_usage, 'prompt_tokens')
        )
        total_completion_tokens = sum(
            msg.models_usage.completion_tokens 
            for msg in result.messages 
            if msg.models_usage and hasattr(msg.models_usage, 'completion_tokens')
        )
        
        print(f"🎯 Prompt Tokens: {total_prompt_tokens}")
        print(f"📝 Completion Tokens: {total_completion_tokens}")
        print(f"💰 总Token数: {total_prompt_tokens + total_completion_tokens}")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        raise
    finally:
        await workflow.close()


async def main():
    """主测试函数"""
    print("🚀 AutoGen编程工作流测试套件")
    print("=" * 50)
    
    tests = [
        ("组件测试", test_workflow_components),
        ("简单工作流测试", test_simple_workflow),
        ("性能基准测试", benchmark_workflow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            await test_func()
            results.append((test_name, "✅ 通过"))
        except Exception as e:
            results.append((test_name, f"❌ 失败: {str(e)}"))
            print(f"❌ {test_name} 失败: {str(e)}")
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    for test_name, status in results:
        print(f"{test_name}: {status}")
    
    # 统计
    passed = sum(1 for _, status in results if "✅" in status)
    total = len(results)
    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
