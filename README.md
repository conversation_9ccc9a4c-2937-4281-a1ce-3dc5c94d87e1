# AutoGen编程工作流

基于最新AutoGen框架的多Agent编程协作系统，实现代码编写、审查和优化的完整工作流。

## 🌟 特性

- **三Agent协作**: 代码编写者、代码审查者、代码优化者
- **最新AutoGen**: 使用AutoGen AgentChat框架 (v0.4+)
- **智能终止**: 基于关键词和消息数量的智能终止条件
- **实时监控**: 支持实时查看Agent对话过程
- **灵活配置**: 支持不同模型和参数配置
- **丰富示例**: 包含数据结构、算法、工具开发等多种示例

## 🏗️ 架构设计

```
用户任务 → Agent1(代码编写) → Agent2(代码审查) → Agent3(代码优化) → 最终结果
    ↑                                                                    ↓
    └─────────────────── 循环迭代直到满足终止条件 ──────────────────────────┘
```

### Agent职责分工

1. **代码编写者 (Coder Agent)**
   - 根据需求编写初始代码
   - 确保代码功能完整性
   - 添加基础注释和文档

2. **代码审查者 (Reviewer Agent)**
   - 检查代码质量和规范性
   - 识别潜在问题和安全漏洞
   - 提出具体改进建议

3. **代码优化者 (Optimizer Agent)**
   - 基于审查意见优化代码
   - 提升性能和可维护性
   - 完善错误处理和边界条件

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置API密钥

```bash
# 设置DeepSeek API密钥（推荐，更经济）
export DEEPSEEK_API_KEY="your-deepseek-api-key"

# 或者设置OpenAI API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 或者创建.env文件
echo "DEEPSEEK_API_KEY=your-deepseek-api-key" > .env
```

**注意**: 项目默认使用DeepSeek模型，性价比更高。如需使用OpenAI模型，请使用 `openai` 配置。

### 3. 基础使用

```python
import asyncio
from autogen_programming_workflow import ProgrammingWorkflow

async def main():
    # 创建工作流
    workflow = ProgrammingWorkflow()
    
    # 定义编程任务
    task = """
    请实现一个Python函数来计算斐波那契数列的第n项。
    要求：
    1. 支持递归和迭代两种实现
    2. 包含性能比较
    3. 添加完整的错误处理
    """
    
    # 运行工作流
    result = await workflow.run_programming_workflow(task)
    
    # 关闭连接
    await workflow.close()

# 运行
asyncio.run(main())
```

### 4. 运行示例

```bash
# 运行所有示例
python workflow_examples.py

# 运行特定示例
python workflow_examples.py data_structure
python workflow_examples.py algorithm
python workflow_examples.py web_scraper
```

## 📚 详细示例

### 示例1：数据结构实现

```python
from workflow_examples import WorkflowExamples

async def run_data_structure_example():
    examples = WorkflowExamples()
    await examples.run_single_example("data_structure")
```

### 示例2：自定义任务

```python
async def custom_task():
    workflow = ProgrammingWorkflow()
    
    task = """
    实现一个线程安全的缓存类，支持LRU淘汰策略。
    要求：
    1. 支持get、put、delete操作
    2. 线程安全
    3. 可配置最大容量
    4. 包含统计信息（命中率等）
    """
    
    result = await workflow.run_programming_workflow(task)
    await workflow.close()
    return result
```

## ⚙️ 配置选项

### 模型配置

```python
# 使用DeepSeek模型（默认，推荐）
workflow = ProgrammingWorkflow()  # 默认使用deepseek-chat

# 使用不同的DeepSeek配置
workflow = ProgrammingWorkflow(config_name="thorough")  # 使用deepseek-reasoner

# 使用OpenAI模型
workflow = ProgrammingWorkflow(config_name="openai")  # 使用gpt-4o
```

### 支持的模型

| 模型 | 特点 | 价格 | 适用场景 |
|------|------|------|----------|
| deepseek-chat | 高质量，经济 | 💰 | 推荐，一般任务 |
| deepseek-reasoner | 推理能力强 | 💰💰 | 复杂逻辑任务 |
| gpt-4o | OpenAI最佳 | 💰💰💰 | 高要求任务 |
| gpt-4o-mini | OpenAI经济 | 💰💰 | 简单任务 |

### 终止条件配置

```python
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination

# 自定义终止条件
custom_termination = (
    TextMentionTermination("FINAL_CODE_READY") |
    MaxMessageTermination(20)  # 最多20轮对话
)
```

### Agent系统消息自定义

```python
# 可以修改Agent的系统消息来调整行为
coder_system_message = """
你是一个专注于性能优化的Python开发者...
"""

reviewer_system_message = """
你是一个严格的代码审查专家，特别关注安全性...
"""
```

## 🔧 高级功能

### 1. 工作流状态管理

```python
# 重置工作流
await workflow.reset_workflow()

# 检查工作流状态
team_messages = workflow.team.messages
```

### 2. 自定义Agent

```python
from autogen_agentchat.agents import AssistantAgent

# 添加专门的测试Agent
test_agent = AssistantAgent(
    name="tester",
    model_client=model_client,
    system_message="你专门负责编写和执行测试用例..."
)
```

### 3. 工具集成

```python
# 为Agent添加工具
async def run_code(code: str) -> str:
    """执行Python代码并返回结果"""
    # 实现代码执行逻辑
    pass

coder_agent.tools = [run_code]
```

## 📊 性能监控

### 消息统计

```python
result = await workflow.run_programming_workflow(task)

print(f"总消息数: {len(result.messages)}")
print(f"停止原因: {result.stop_reason}")

# 分析每个Agent的贡献
for message in result.messages:
    print(f"{message.source}: {len(message.content)} 字符")
```

### Token使用统计

```python
total_prompt_tokens = sum(
    msg.models_usage.prompt_tokens 
    for msg in result.messages 
    if msg.models_usage
)
total_completion_tokens = sum(
    msg.models_usage.completion_tokens 
    for msg in result.messages 
    if msg.models_usage
)
```

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   export OPENAI_API_KEY="your-correct-api-key"
   ```

2. **依赖版本冲突**
   ```bash
   pip install --upgrade autogen-agentchat autogen-ext
   ```

3. **内存不足**
   - 减少最大消息数限制
   - 使用更小的模型（如gpt-4o-mini）

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
workflow = ProgrammingWorkflow()
result = await workflow.run_programming_workflow(task, show_console=True)
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🔗 相关链接

- [AutoGen官方文档](https://microsoft.github.io/autogen/)
- [AutoGen GitHub](https://github.com/microsoft/autogen)
- [OpenAI API文档](https://platform.openai.com/docs)

## 📞 支持

如有问题或建议，请创建Issue或联系维护者。
