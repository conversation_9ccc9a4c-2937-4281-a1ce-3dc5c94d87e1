{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Prism.js 代码高亮 -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/toolbar/prism-toolbar.min.css" rel="stylesheet">

<style>
    .chat-container {
        height: 60vh;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.375rem;
    }

    .message {
        margin-bottom: 1.5rem;
        padding: 1rem;
        border-radius: 0.5rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .message.user {
        background: #e3f2fd;
        margin-left: 2rem;
    }

    .message.assistant {
        background: #f3e5f5;
        margin-right: 2rem;
    }

    .message.system {
        background: #fff3e0;
        border-left: 4px solid #ff9800;
    }

    .loading {
        display: none;
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .code-block {
        position: relative;
        margin: 1rem 0;
    }

    .code-header {
        background: #2d3748;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem 0.375rem 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
    }

    .code-actions {
        display: flex;
        gap: 0.5rem;
    }

    .code-actions button {
        background: rgba(255,255,255,0.1) !important;
        border: 1px solid rgba(255,255,255,0.2) !important;
        color: white !important;
        padding: 0.25rem 0.5rem !important;
        border-radius: 0.25rem !important;
        font-size: 0.75rem !important;
        cursor: pointer !important;
        transition: background 0.2s !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 0.25rem !important;
    }

    .code-actions button:hover {
        background: rgba(255,255,255,0.2) !important;
    }

    .code-actions button i {
        font-size: 0.7rem !important;
    }

    .markdown-content {
        line-height: 1.6;
    }

    .markdown-content h1, .markdown-content h2, .markdown-content h3 {
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .markdown-content ul, .markdown-content ol {
        margin: 0.5rem 0;
        padding-left: 1.5rem;
    }

    .markdown-content blockquote {
        border-left: 4px solid #3498db;
        padding-left: 1rem;
        margin: 1rem 0;
        color: #7f8c8d;
        font-style: italic;
    }

    .stream-indicator {
        display: none;
        padding: 0.5rem;
        background: #e8f5e8;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
        font-size: 0.875rem;
        color: #2e7d32;
    }

    .stream-indicator.active {
        display: block;
    }

    .agent-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .agent-badge.coder {
        background: #e3f2fd;
        color: #1976d2;
    }

    .agent-badge.reviewer {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .agent-badge.optimizer {
        background: #e8f5e8;
        color: #388e3c;
    }

    /* 代码块样式覆盖 */
    .code-block pre {
        margin: 0 !important;
        border-radius: 0 0 0.375rem 0.375rem !important;
        background: #1e1e1e !important;
    }

    .code-block pre code {
        background: #1e1e1e !important;
        color: #d4d4d4 !important;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
    }

    /* Prism.js 主题覆盖 */
    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata {
        color: #6a9955 !important;
    }

    .token.punctuation {
        color: #d4d4d4 !important;
    }

    .token.property,
    .token.tag,
    .token.boolean,
    .token.number,
    .token.constant,
    .token.symbol,
    .token.deleted {
        color: #b5cea8 !important;
    }

    .token.selector,
    .token.attr-name,
    .token.string,
    .token.char,
    .token.builtin,
    .token.inserted {
        color: #ce9178 !important;
    }

    .token.operator,
    .token.entity,
    .token.url,
    .language-css .token.string,
    .style .token.string {
        color: #d4d4d4 !important;
    }

    .token.atrule,
    .token.attr-value,
    .token.keyword {
        color: #569cd6 !important;
    }

    .token.function,
    .token.class-name {
        color: #dcdcaa !important;
    }

    .token.regex,
    .token.important,
    .token.variable {
        color: #d16969 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-comments text-primary me-2"></i>AI编程助手对话
                </h5>
                <div class="d-flex align-items-center">
                    <label for="configSelect" class="form-label me-2 mb-0">配置模式：</label>
                    <select class="form-select form-select-sm" id="configSelect" style="width: auto;">
                        {% for config in configs %}
                        <option value="{{ config.name }}" 
                                {% if config.name == 'default' %}selected{% endif %}>
                            {{ config.display_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 聊天容器 -->
                <div id="chatContainer" class="chat-container">
                    <div class="message system">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-robot text-primary me-2 mt-1"></i>
                            <div>
                                <strong>AutoGen全栈编程助手</strong>
                                <p class="mb-0 mt-1">
                                    您好！我是基于DeepSeek的多Agent全栈编程助手。我可以帮您：
                                </p>
                                <ul class="mt-2 mb-0">
                                    <li>🌐 <strong>前端开发</strong>：JavaScript/TypeScript, React, Vue, Angular</li>
                                    <li>⚙️ <strong>后端开发</strong>：Python, Java, Node.js, Go, C#, PHP</li>
                                    <li>📱 <strong>移动开发</strong>：React Native, Flutter, Swift, Kotlin</li>
                                    <li>🗄️ <strong>数据库</strong>：SQL, NoSQL, Redis设计与优化</li>
                                    <li>☁️ <strong>云服务</strong>：AWS, Azure, GCP部署方案</li>
                                    <li>🔍 <strong>代码审查</strong>：多语言代码质量分析</li>
                                    <li>⚡ <strong>性能优化</strong>：算法优化和架构改进</li>
                                </ul>
                                <p class="mt-2 mb-0 small text-muted">
                                    请描述您的编程需求，我将自动选择最适合的技术栈，并通过三个专业Agent为您提供完整的解决方案。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 加载指示器 -->
                <div id="loadingIndicator" class="loading">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">处理中...</span>
                    </div>
                    <p class="mt-2 mb-0">AI正在思考中，请稍候...</p>
                </div>
            </div>
            <div class="card-footer">
                <!-- 输入区域 -->
                <div class="input-group">
                    <textarea class="form-control" id="messageInput" rows="3"
                              placeholder="请描述您的编程任务，例如：用React实现一个待办事项应用、设计一个RESTful API、优化数据库查询性能..."
                              style="resize: none;"></textarea>
                    <button class="btn btn-primary" type="button" id="sendButton">
                        <i class="fas fa-paper-plane me-1"></i>发送
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        提示：描述越详细，AI生成的代码质量越高
                    </small>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm me-2" id="clearButton">
                            <i class="fas fa-trash me-1"></i>清空对话
                        </button>
                        <button class="btn btn-outline-info btn-sm" id="exportButton">
                            <i class="fas fa-download me-1"></i>导出对话
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 示例任务模态框 -->
<div class="modal fade" id="examplesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>示例任务
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>数据结构与算法</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="实现一个二叉搜索树类，支持插入、删除、搜索和遍历操作，包含完整的错误处理">
                                二叉搜索树实现
                            </a>
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="实现快速排序算法，包含递归和迭代两种版本，添加性能测试和比较">
                                快速排序算法
                            </a>
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="设计并实现一个LRU缓存类，支持O(1)时间复杂度的get和put操作">
                                LRU缓存实现
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>实用工具开发</h6>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="创建一个文件管理工具类，支持文件复制、移动、删除和批量操作">
                                文件管理工具
                            </a>
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="实现一个简单的HTTP客户端类，支持GET、POST请求和异步操作">
                                HTTP客户端
                            </a>
                            <a href="#" class="list-group-item list-group-item-action example-item"
                               data-task="设计一个配置文件管理器，支持JSON、YAML格式的读写和验证">
                                配置管理器
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作按钮 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1000;">
    <button class="btn btn-info btn-sm mb-2 d-block" data-bs-toggle="modal" data-bs-target="#examplesModal">
        <i class="fas fa-lightbulb me-1"></i>示例任务
    </button>
    <button class="btn btn-secondary btn-sm d-block" id="scrollToBottom">
        <i class="fas fa-arrow-down me-1"></i>滚动到底部
    </button>
</div>
{% endblock %}

{% block extra_js %}
<!-- Prism.js 代码高亮 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/toolbar/prism-toolbar.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

<!-- Marked.js Markdown 解析 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js"></script>

<script>
    let isProcessing = false;
    let currentEventSource = null;

    // DOM元素
    const chatContainer = document.getElementById('chatContainer');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const configSelect = document.getElementById('configSelect');
    const clearButton = document.getElementById('clearButton');
    const exportButton = document.getElementById('exportButton');
    
    // 发送消息（流式响应）
    async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isProcessing) return;

        isProcessing = true;
        sendButton.disabled = true;
        messageInput.disabled = true;

        // 添加用户消息
        addMessage('user', message, 'You');
        messageInput.value = '';

        // 显示流式指示器
        const streamIndicator = addStreamIndicator();
        scrollToBottom();

        try {
            // 关闭之前的连接
            if (currentEventSource) {
                currentEventSource.close();
            }

            // 创建API请求
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task: message,
                    config: configSelect.value
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 处理JSON响应
            const data = await response.json();

            // 更新状态指示器
            updateStreamIndicator(streamIndicator, '🚀 开始处理请求...');

            // 处理响应数据
            if (data.messages && Array.isArray(data.messages)) {
                for (const message of data.messages) {
                    let agentName = '';
                    let agentIcon = '';
                    let agentClass = '';

                    switch(message.source) {
                        case 'coder':
                            agentName = '编程专家';
                            agentIcon = 'fas fa-code';
                            agentClass = 'coder';
                            break;
                        case 'reviewer':
                            agentName = '代码审查专家';
                            agentIcon = 'fas fa-search';
                            agentClass = 'reviewer';
                            break;
                        case 'optimizer':
                            agentName = '性能优化专家';
                            agentIcon = 'fas fa-tachometer-alt';
                            agentClass = 'optimizer';
                            break;
                        default:
                            agentName = 'AI助手';
                            agentIcon = 'fas fa-robot';
                            agentClass = 'assistant';
                    }

                    // 直接显示消息内容
                    addMessage('assistant', message.content, agentName, agentIcon, agentClass);
                }

                updateStreamIndicator(streamIndicator, `✅ 任务完成！共生成 ${data.messages.length} 条消息`);
                showAlert(`任务完成！共生成 ${data.messages.length} 条消息`, 'success');
            } else {
                throw new Error('响应格式错误');
            }

        } catch (error) {
            console.error('Stream error:', error);
            addMessage('system', `网络错误：${error.message}`, '系统', 'fas fa-exclamation-triangle');
            showAlert('网络连接失败，请检查网络设置', 'danger');
        } finally {
            removeStreamIndicator(streamIndicator);
            isProcessing = false;
            sendButton.disabled = false;
            messageInput.disabled = false;
            messageInput.focus();
            scrollToBottom();
        }
    }







    // 添加消息到聊天容器
    function addMessage(type, content, sender, icon = '', agentClass = '') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        let iconHtml = '';
        if (icon) {
            iconHtml = `<i class="${icon} me-2 mt-1"></i>`;
        } else if (type === 'user') {
            iconHtml = '<i class="fas fa-user me-2 mt-1"></i>';
        } else if (type === 'assistant') {
            iconHtml = '<i class="fas fa-robot me-2 mt-1"></i>';
        } else {
            iconHtml = '<i class="fas fa-info-circle me-2 mt-1"></i>';
        }

        // 添加 Agent 徽章
        let agentBadge = '';
        if (agentClass) {
            agentBadge = `<span class="agent-badge ${agentClass}">${sender}</span><br>`;
        }

        const formattedContent = formatContent(content);

        messageDiv.innerHTML = `
            <div class="d-flex align-items-start">
                ${iconHtml}
                <div class="flex-grow-1">
                    ${agentBadge}
                    ${agentBadge ? '' : `<strong>${sender}</strong>`}
                    <div class="mt-1 markdown-content">${formattedContent}</div>
                    <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                </div>
            </div>
        `;

        chatContainer.appendChild(messageDiv);

        // 高亮代码
        Prism.highlightAllUnder(messageDiv);
        scrollToBottom();
    }

    // 格式化内容（支持 Markdown 和代码块）
    function formatContent(content) {
        // 保存代码块的占位符映射
        const codeBlocks = {};
        let codeBlockIndex = 0;

        // 首先提取并替换代码块为占位符，避免被 marked.js 处理
        content = content.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, language, code) => {
            const lang = language || 'text';
            const filename = getFilenameFromLanguage(lang);
            const codeId = 'code-' + Math.random().toString(36).substr(2, 9);
            const placeholder = `<!--CODE_BLOCK_${codeBlockIndex}-->`;

            codeBlocks[placeholder] = `
                <div class="code-block">
                    <div class="code-header">
                        <span><i class="fas fa-file-code me-1"></i>${filename}</span>
                        <div class="code-actions">
                            <button onclick="copyCode('${codeId}')" title="复制代码">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                            <button onclick="downloadCode('${codeId}', '${filename}')" title="下载文件">
                                <i class="fas fa-download"></i> 下载
                            </button>
                        </div>
                    </div>
                    <pre class="line-numbers"><code id="${codeId}" class="language-${lang}">${code.trim()}</code></pre>
                </div>
            `;

            codeBlockIndex++;
            return placeholder;
        });

        // 处理行内代码（在 marked.js 之前）
        content = content.replace(/`([^`\n]+)`/g, '<code class="language-text">$1</code>');

        // 使用 marked.js 处理其他 Markdown
        try {
            content = marked.parse(content);
        } catch (e) {
            console.warn('Markdown parsing failed:', e);
        }

        // 最后恢复代码块（HTML注释不会被marked.js处理）
        Object.keys(codeBlocks).forEach(placeholder => {
            // HTML注释格式的占位符不需要转义
            content = content.replace(new RegExp(placeholder, 'g'), codeBlocks[placeholder]);
        });

        return content;
    }

    // 根据语言获取文件名
    function getFilenameFromLanguage(lang) {
        const extensions = {
            'javascript': 'script.js',
            'python': 'script.py',
            'html': 'index.html',
            'css': 'style.css',
            'java': 'Main.java',
            'cpp': 'main.cpp',
            'c': 'main.c',
            'php': 'script.php',
            'ruby': 'script.rb',
            'go': 'main.go',
            'rust': 'main.rs',
            'sql': 'query.sql',
            'json': 'data.json',
            'xml': 'data.xml',
            'yaml': 'config.yaml',
            'bash': 'script.sh',
            'powershell': 'script.ps1'
        };
        return extensions[lang.toLowerCase()] || `code.${lang}`;
    }

    // HTML 转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 复制代码
    function copyCode(codeId) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
            navigator.clipboard.writeText(codeElement.textContent).then(() => {
                showAlert('代码已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showAlert('复制失败', 'danger');
            });
        }
    }

    // 下载代码
    function downloadCode(codeId, filename) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
            const blob = new Blob([codeElement.textContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
            showAlert(`文件 ${filename} 已下载`, 'success');
        }
    }
    
    // 添加流式指示器
    function addStreamIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'stream-indicator active';
        indicator.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在连接...';
        chatContainer.appendChild(indicator);
        return indicator;
    }

    // 更新流式指示器
    function updateStreamIndicator(indicator, message) {
        if (indicator) {
            indicator.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${message}`;
        }
    }

    // 移除流式指示器
    function removeStreamIndicator(indicator) {
        if (indicator && indicator.parentNode) {
            indicator.parentNode.removeChild(indicator);
        }
    }

    // 滚动到底部
    function scrollToBottom() {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // 清空对话
    function clearChat() {
        if (confirm('确定要清空所有对话记录吗？')) {
            const systemMessage = chatContainer.querySelector('.message.system');
            chatContainer.innerHTML = '';
            if (systemMessage) {
                chatContainer.appendChild(systemMessage);
            }
            showAlert('对话记录已清空', 'info');
        }
    }
    
    // 导出对话
    function exportChat() {
        const messages = Array.from(chatContainer.querySelectorAll('.message')).map(msg => {
            const senderElement = msg.querySelector('strong') || msg.querySelector('.agent-badge');
            const sender = senderElement ? senderElement.textContent : 'Unknown';
            const content = msg.querySelector('.markdown-content').textContent;
            const time = msg.querySelector('small').textContent;
            return `[${time}] ${sender}: ${content}`;
        }).join('\n\n');

        const blob = new Blob([messages], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `autogen-chat-${new Date().toISOString().slice(0, 10)}.txt`;
        a.click();
        URL.revokeObjectURL(url);

        showAlert('对话记录已导出', 'success');
    }

    // 显示警告消息
    function showAlert(message, type = 'info') {
        // 创建警告元素
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        // 自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
    
    // 事件监听器
    sendButton.addEventListener('click', sendMessage);
    clearButton.addEventListener('click', clearChat);
    exportButton.addEventListener('click', exportChat);
    
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    document.getElementById('scrollToBottom').addEventListener('click', scrollToBottom);
    
    // 示例任务点击事件
    document.querySelectorAll('.example-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            messageInput.value = this.dataset.task;
            bootstrap.Modal.getInstance(document.getElementById('examplesModal')).hide();
            messageInput.focus();
        });
    });
    
    // 自动聚焦输入框
    messageInput.focus();
</script>
{% endblock %}
