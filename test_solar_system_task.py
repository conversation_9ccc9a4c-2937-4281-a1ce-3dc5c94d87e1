#!/usr/bin/env python3
"""
测试太阳系动画任务的脚本
通过 AutoGen 工作流处理 "使用three.js实现基于纯html的太阳系运行动画" 任务
"""

import asyncio
import json
from autogen_programming_workflow import ProgrammingWorkflow


async def test_solar_system_task():
    """测试太阳系动画任务"""
    
    # 定义任务
    task = "使用three.js实现基于纯html的太阳系运行动画。要求包含太阳和八大行星，具有真实的运行轨道和自转效果，支持动画控制和视角切换。"
    
    print("🚀 开始测试太阳系动画任务")
    print(f"📝 任务描述: {task}")
    print("=" * 80)
    
    try:
        # 创建工作流实例（使用默认配置）
        workflow = ProgrammingWorkflow(config_name="default")
        
        # 执行任务
        result = await workflow.run_programming_workflow(task, show_console=True)
        
        print("\n" + "=" * 80)
        print("✅ 任务执行完成!")
        print(f"📊 总消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        # 保存结果到文件
        output_data = {
            "task": task,
            "stop_reason": result.stop_reason,
            "message_count": len(result.messages),
            "messages": []
        }
        
        # 提取消息内容
        for i, msg in enumerate(result.messages):
            if hasattr(msg, 'source') and hasattr(msg, 'content'):
                output_data["messages"].append({
                    "index": i + 1,
                    "source": msg.source,
                    "content": msg.content[:1000] + "..." if len(msg.content) > 1000 else msg.content
                })
        
        # 保存到JSON文件
        with open("solar_system_task_result.json", "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print("💾 结果已保存到 solar_system_task_result.json")
        
        # 关闭工作流
        await workflow.close()
        
        return result
        
    except Exception as e:
        print(f"❌ 任务执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_simple_task():
    """测试一个简单任务，验证工作流基本功能"""
    
    task = "编写一个Python函数，计算斐波那契数列的第n项"
    
    print("🧪 开始测试简单任务")
    print(f"📝 任务描述: {task}")
    print("=" * 80)
    
    try:
        workflow = ProgrammingWorkflow(config_name="fast")
        result = await workflow.run_programming_workflow(task, show_console=True)
        
        print("\n" + "=" * 80)
        print("✅ 简单任务执行完成!")
        print(f"📊 总消息数: {len(result.messages)}")
        print(f"🛑 停止原因: {result.stop_reason}")
        
        await workflow.close()
        return result
        
    except Exception as e:
        print(f"❌ 简单任务执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def main():
    """主函数"""
    print("🔧 AutoGen 工作流测试程序")
    print("=" * 80)
    
    # 首先测试简单任务，验证工作流基本功能
    print("\n1️⃣ 测试简单任务...")
    simple_result = await test_simple_task()
    
    if simple_result is None:
        print("❌ 简单任务失败，停止测试")
        return
    
    print("\n" + "=" * 80)
    input("✅ 简单任务成功！按 Enter 继续测试太阳系动画任务...")
    
    # 测试太阳系动画任务
    print("\n2️⃣ 测试太阳系动画任务...")
    solar_result = await test_solar_system_task()
    
    if solar_result:
        print("\n🎉 所有测试完成！")
    else:
        print("\n❌ 太阳系动画任务失败")


if __name__ == "__main__":
    asyncio.run(main())
