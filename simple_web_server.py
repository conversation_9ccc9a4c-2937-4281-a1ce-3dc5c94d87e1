"""
简化版Web服务器
用于演示Web界面功能，不依赖AutoGen
"""

import asyncio
import logging
import json
import time
from datetime import datetime

from fastapi import FastAPI, Request, Form, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from starlette.middleware.sessions import SessionMiddleware

from config import get_web_config


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取配置
web_config = get_web_config()

# 创建FastAPI应用
app = FastAPI(
    title="AutoGen编程工作流Web界面 (演示版)",
    description="基于DeepSeek的多Agent编程协作Web服务 - 演示版本",
    version="1.0.0-demo"
)

# 添加会话中间件
app.add_middleware(
    SessionMiddleware,
    secret_key=web_config.secret_key,
    max_age=web_config.session_timeout
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=web_config.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模板
templates = Jinja2Templates(directory="templates")


def check_login(request: Request) -> bool:
    """检查用户是否已登录"""
    return request.session.get("logged_in", False)


def require_login(request: Request):
    """要求用户登录"""
    if not check_login(request):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录"
        )


@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路径，重定向到登录页"""
    if check_login(request):
        return RedirectResponse(url="/dashboard", status_code=302)
    return RedirectResponse(url="/login", status_code=302)


@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """登录页面"""
    if check_login(request):
        return RedirectResponse(url="/dashboard", status_code=302)
    
    return templates.TemplateResponse("login.html", {
        "request": request,
        "title": "AutoGen工作流 - 登录"
    })


@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...)
):
    """处理登录"""
    if username == web_config.username and password == web_config.password:
        request.session["logged_in"] = True
        request.session["username"] = username
        request.session["login_time"] = datetime.now().isoformat()
        
        logger.info(f"用户 {username} 登录成功")
        return RedirectResponse(url="/dashboard", status_code=302)
    else:
        logger.warning(f"用户 {username} 登录失败")
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "AutoGen工作流 - 登录",
            "error": "用户名或密码错误"
        })


@app.get("/logout")
async def logout(request: Request):
    """退出登录"""
    username = request.session.get("username", "unknown")
    request.session.clear()
    logger.info(f"用户 {username} 退出登录")
    return RedirectResponse(url="/login", status_code=302)


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """引导页/仪表板"""
    require_login(request)
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "AutoGen工作流 - 控制台",
        "username": request.session.get("username"),
        "api_domain": web_config.api_domain,
        "api_port": web_config.api_port
    })


@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """对话页面"""
    require_login(request)
    
    # 模拟配置
    configs = [
        {
            "name": "default",
            "display_name": "default (deepseek-chat)",
            "model_type": "deepseek-chat",
            "mode": "standard"
        },
        {
            "name": "fast",
            "display_name": "fast (deepseek-chat)",
            "model_type": "deepseek-chat",
            "mode": "fast"
        },
        {
            "name": "thorough",
            "display_name": "thorough (deepseek-reasoner)",
            "model_type": "deepseek-reasoner",
            "mode": "thorough"
        }
    ]
    
    return templates.TemplateResponse("chat.html", {
        "request": request,
        "title": "AutoGen工作流 - 对话",
        "username": request.session.get("username"),
        "configs": configs
    })


@app.get("/api-docs", response_class=HTMLResponse)
async def api_docs_page(request: Request):
    """API接入说明页面"""
    require_login(request)

    return templates.TemplateResponse("api_docs.html", {
        "request": request,
        "title": "AutoGen工作流 - API接入",
        "username": request.session.get("username"),
        "api_domain": web_config.api_domain,
        "api_port": web_config.api_port,
        "api_key": web_config.api_key
    })


@app.post("/api/chat")
async def chat_api(request: Request):
    """聊天API接口 (演示版)"""
    require_login(request)
    
    try:
        data = await request.json()
        task = data.get("task", "").strip()
        config_name = data.get("config", "default")
        
        if not task:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "任务描述不能为空"}
            )
        
        # 模拟处理时间
        await asyncio.sleep(2)
        
        # 模拟三个Agent的响应
        messages = [
            {
                "source": "coder",
                "content": f"""我是代码编写者。根据您的需求："{task}"

我为您编写了以下解决方案：

```python
def solution():
    \"\"\"
    针对任务: {task[:50]}...
    \"\"\"
    print("正在处理您的编程任务...")
    # 具体实现
    return "任务完成"

# 使用示例
result = solution()
print(result)
```

这是一个基础实现，包含了核心功能。""",
                "type": "text"
            },
            {
                "source": "reviewer",
                "content": f"""我是代码审查者。我已经审查了上述代码：

✅ **优点:**
- 代码结构清晰
- 包含文档字符串
- 提供使用示例

🔧 **改进建议:**
- 建议添加错误处理
- 可以增加输入验证
- 建议添加类型注解

总体来说，代码质量良好，建议按照上述建议进行优化。""",
                "type": "text"
            },
            {
                "source": "optimizer",
                "content": f"""我是代码优化者。基于审查意见，我优化了代码：

```python
from typing import Any
import logging

def optimized_solution(task_description: str = "{task[:30]}...", input_data: Any = None) -> str:
    \"\"\"
    优化后的解决方案

    Args:
        task_description: 任务描述
        input_data: 输入数据

    Returns:
        str: 处理结果
    \"\"\"
    try:
        logging.info("开始处理任务...")

        # 输入验证
        if input_data is not None:
            logging.info(f"处理输入: {{input_data}}")

        # 核心逻辑
        result = f"优化完成 - {{task_description}}"
        logging.info("任务执行成功")
        return result

    except Exception as e:
        logging.error(f"执行失败: {{e}}")
        raise

# 使用示例
try:
    result = optimized_solution()
    print(f"✅ {{result}}")
except Exception as e:
    print(f"❌ 错误: {{e}}")
```

**优化说明:**
- ✅ 添加了类型注解
- ✅ 增加了异常处理
- ✅ 改进了日志记录
- ✅ 优化了代码结构

OPTIMIZATION_COMPLETE""",
                "type": "text"
            }
        ]
        
        return JSONResponse(content={
            "success": True,
            "messages": messages,
            "stop_reason": "OPTIMIZATION_COMPLETE",
            "message_count": len(messages)
        })
        
    except Exception as e:
        logger.error(f"聊天API错误: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"处理失败: {str(e)}"}
        )


@app.get("/api/configs")
async def get_configs_api(request: Request):
    """获取配置API"""
    require_login(request)
    
    configs = [
        {
            "name": "default",
            "model_type": "deepseek-chat",
            "mode": "standard",
            "max_messages": 15
        },
        {
            "name": "fast",
            "model_type": "deepseek-chat",
            "mode": "fast",
            "max_messages": 8
        },
        {
            "name": "thorough",
            "model_type": "deepseek-reasoner",
            "mode": "thorough",
            "max_messages": 25
        }
    ]
    
    return {"configs": configs}


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    if exc.status_code == 401:
        return RedirectResponse(url="/login", status_code=302)
    
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )


def run_web_server():
    """运行Web服务器"""
    logger.info(f"🌐 启动AutoGen Web服务 (演示版)")
    logger.info(f"🔗 Web域名: {web_config.web_domain}")
    logger.info(f"👤 登录用户名: {web_config.username}")
    logger.info(f"🔑 登录密码: {web_config.password}")
    logger.info(f"📱 Web界面: http://{web_config.web_domain}:{web_config.web_port}")
    
    uvicorn.run(
        "simple_web_server:app",
        host="0.0.0.0",
        port=web_config.web_port,
        reload=web_config.debug,
        log_level="info"
    )


if __name__ == "__main__":
    run_web_server()
